# Auris Compliance Web - Sitemap

## Site Structure

### Main Pages
- [`/`](src/app/page.js) - Homepage
- [`/login`](src/app/login/page.js) - User Login
- [`/demo`](src/app/demo/page.js) - Product Demo
- [`/contact`](src/app/contact/page.js) - Contact Information
- [`/waitlist`](src/app/waitlist/page.js) - Waitlist Registration

### Product Section
- [`/product`](src/app/product/page.js) - Product Overview
- [`/product/grcos-architecture`](src/app/product/grcos-architecture/page.js) - GRCOS Architecture
- [`/product/integrations`](src/app/product/integrations/page.js) - Integrations
- [`/product/platform-compliance`](src/app/product/platform-compliance/page.js) - Platform Compliance

#### Product Modules
- [`/product/modules/actioncentre`](src/app/product/modules/actioncentre/page.js) - ActionCentre Module
- [`/product/modules/compliancecentre`](src/app/product/modules/compliancecentre/page.js) - ComplianceCentre Module
- [`/product/modules/lighthouse`](src/app/product/modules/lighthouse/page.js) - LightHouse Module
- [`/product/modules/trustcentre`](src/app/product/modules/trustcentre/page.js) - TrustCentre Module

### Solutions Section
- [`/solutions`](src/app/solutions/page.js) - Solutions Overview
- [`/solutions/frameworks`](src/app/solutions/frameworks/page.js) - Unified Frameworks Page (with anchor sections for each framework)
- [`/solutions/industry/[industry]`](src/app/solutions/industry/[industry]/page.js) - Dynamic Industry Solutions
- [`/solutions/size/startups`](src/app/solutions/size/startups/page.js) - Startups Solutions
- [`/solutions/size/smes`](src/app/solutions/size/smes/page.js) - SME Solutions
- [`/solutions/size/enterprise`](src/app/solutions/size/enterprise/page.js) - Enterprise Solutions

### Company Section
- [`/company`](src/app/company/page.js) - Company Overview
- [`/company/team`](src/app/company/team/page.js) - Team Information
- [`/company/mission`](src/app/company/mission/page.js) - Mission Statement
- [`/company/careers`](src/app/company/careers/page.js) - Career Opportunities
- [`/company/partners`](src/app/company/partners/page.js) - Partners

### Resources Section
- [`/resources`](src/app/resources/page.js) - Resources Overview
- [`/resources/blog`](src/app/resources/blog/page.js) - Blog
- [`/resources/case-studies`](src/app/resources/case-studies/page.js) - Customer Success Stories
- [`/resources/guides`](src/app/resources/guides/page.js) - Implementation Guides & Best Practices
- [`/resources/docs`](src/app/resources/docs/page.js) - Documentation
- [`/resources/docs/api`](src/app/resources/docs/api/page.js) - API Documentation
- [`/resources/faq`](src/app/resources/faq/page.js) - Frequently Asked Questions
- [`/resources/events-webinars`](src/app/resources/events-webinars/page.js) - Events & Webinars

### Contact & Support
- [`/contact/support`](src/app/contact/support/page.js) - Support Center

### Legal Pages
- [`/legal/privacy-policy`](src/app/legal/privacy-policy/page.js) - Privacy Policy
- [`/legal/terms-of-service`](src/app/legal/terms-of-service/page.js) - Terms of Service
- [`/legal/cookie-policy`](src/app/legal/cookie-policy/page.js) - Cookie Policy
- [`/legal/security`](src/app/legal/security/page.js) - Security Information

### Error Pages
- [`/not-found`](src/app/not-found.js) - 404 Not Found Page

## Key Components

### Layout Components
- [`Header`](src/components/layout/Header.js) - Site Header
- [`Footer`](src/components/layout/Footer.js) - Site Footer
- [`MegaMenu`](src/components/layout/MegaMenu.js) - Navigation Menu

### UI Components
- [`Button`](src/components/ui/Button.js) - Custom Button Component
- [`Navigation Menu`](src/components/ui/navigation-menu.jsx) - Navigation Component
- [`Theme Toggle`](src/components/ui/theme-toggle.js) - Dark/Light Mode Toggle
- [`Smooth Cursor`](src/components/ui/smooth-cursor.jsx) - Custom Cursor Effect

### Feature Sections
- [`Features Demo 1`](src/components/features-section-demo-1.jsx) - Product Features Showcase
- [`Features Demo 2`](src/components/features-section-demo-2.jsx) - Additional Features
- [`Features Demo 3`](src/components/features-section-demo-3.jsx) - Extended Features

## Development

This is a Next.js application using the App Router structure. To run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.
