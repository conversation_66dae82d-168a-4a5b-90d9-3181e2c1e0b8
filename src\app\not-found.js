import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowLeft, Home, Search } from "lucide-react";

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-4">
      <div className="text-center">
        <div className="text-6xl font-bold text-primary mb-4">404</div>
        <h1 className="text-3xl font-bold tracking-tight mb-4">Page Not Found</h1>
        <p className="text-lg text-muted-foreground mb-8 max-w-md">
          Sorry, we couldn&apos;t find the page you&apos;re looking for. It might have been moved, deleted, or you entered the wrong URL.
        </p>
        
        <div className="flex items-center justify-center gap-4">
          <Button asChild>
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/contact">
              Contact Support
            </Link>
          </Button>
        </div>
        
        <div className="mt-8 pt-8 border-t">
          <p className="text-sm text-muted-foreground mb-4">
            Looking for something specific? Try these popular pages:
          </p>
          <div className="flex flex-wrap items-center justify-center gap-2">
            <Link href="/product" className="text-sm text-primary hover:underline">
              Product
            </Link>
            <span className="text-muted-foreground">•</span>
            <Link href="/solutions" className="text-sm text-primary hover:underline">
              Solutions
            </Link>
            <span className="text-muted-foreground">•</span>
            <Link href="/demo" className="text-sm text-primary hover:underline">
              Demo
            </Link>
            <span className="text-muted-foreground">•</span>
            <Link href="/resources" className="text-sm text-primary hover:underline">
              Resources
            </Link>
            <span className="text-muted-foreground">•</span>
            <Link href="/company" className="text-sm text-primary hover:underline">
              Company
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
