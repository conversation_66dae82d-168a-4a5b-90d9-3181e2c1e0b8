import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { <PERSON>R<PERSON>, FileText, Clock, Users, CheckCircle, Download, BookOpen, Shield } from "lucide-react";

export default function GuidesPage() {
  const guides = [
    {
      id: "iso-27001-implementation",
      title: "ISO 27001 Implementation Roadmap",
      description: "Complete step-by-step guide to implementing ISO 27001 information security management system in your organization.",
      category: "Implementation",
      framework: "ISO 27001",
      readTime: "15 min read",
      difficulty: "Intermediate",
      downloadable: true,
      featured: true,
      topics: [
        "Gap analysis and risk assessment",
        "Control implementation strategy",
        "Documentation requirements",
        "Internal audit preparation",
        "Certification process"
      ]
    },
    {
      id: "soc2-preparation-checklist",
      title: "SOC 2 Type II Preparation Checklist",
      description: "Essential checklist and timeline for preparing your organization for SOC 2 Type II audit.",
      category: "Preparation",
      framework: "SOC 2",
      readTime: "10 min read",
      difficulty: "Beginner",
      downloadable: true,
      featured: true,
      topics: [
        "Trust service criteria overview",
        "Control environment setup",
        "Evidence collection process",
        "Audit timeline planning",
        "Common pitfalls to avoid"
      ]
    },
    {
      id: "hipaa-compliance-healthcare",
      title: "HIPAA Compliance for Healthcare Technology",
      description: "Comprehensive guide for healthcare technology companies to achieve and maintain HIPAA compliance.",
      category: "Industry Guide",
      framework: "HIPAA",
      readTime: "20 min read",
      difficulty: "Advanced",
      downloadable: true,
      featured: true,
      topics: [
        "Administrative safeguards",
        "Physical safeguards",
        "Technical safeguards",
        "Business associate agreements",
        "Breach notification procedures"
      ]
    },
    {
      id: "multi-framework-strategy",
      title: "Multi-Framework Compliance Strategy",
      description: "How to implement unified controls that satisfy multiple compliance frameworks simultaneously.",
      category: "Strategy",
      framework: "Multiple",
      readTime: "12 min read",
      difficulty: "Advanced",
      downloadable: false,
      featured: false,
      topics: [
        "Framework mapping techniques",
        "Unified control design",
        "Cross-framework reporting",
        "Audit coordination",
        "Cost optimization strategies"
      ]
    },
    {
      id: "pci-dss-payment-security",
      title: "PCI DSS Payment Security Guide",
      description: "Essential guide for organizations handling payment card data to achieve PCI DSS compliance.",
      category: "Implementation",
      framework: "PCI DSS",
      readTime: "18 min read",
      difficulty: "Intermediate",
      downloadable: true,
      featured: false,
      topics: [
        "Network security requirements",
        "Data protection measures",
        "Vulnerability management",
        "Access control implementation",
        "Regular monitoring procedures"
      ]
    },
    {
      id: "gdpr-data-protection",
      title: "GDPR Data Protection Implementation",
      description: "Step-by-step guide to implementing GDPR data protection requirements for EU operations.",
      category: "Implementation",
      framework: "GDPR",
      readTime: "16 min read",
      difficulty: "Intermediate",
      downloadable: true,
      featured: false,
      topics: [
        "Data processing records",
        "Consent management",
        "Data subject rights",
        "Privacy by design",
        "Data protection impact assessments"
      ]
    }
  ];

  const categories = [...new Set(guides.map(guide => guide.category))];
  const frameworks = [...new Set(guides.map(guide => guide.framework))];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Implementation <span className="text-primary">Guides</span> & Best Practices
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Step-by-step guides, checklists, and best practices to help you successfully 
              implement and maintain compliance across all major frameworks.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="#featured-guides">
                  Browse Guides
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Platform Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Guides */}
      <section id="featured-guides" className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Featured Implementation Guides
              </h2>
              <p className="text-lg text-muted-foreground">
                Our most comprehensive and popular guides for compliance implementation
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {guides.filter(guide => guide.featured).map((guide) => (
                <div key={guide.id} className="rounded-lg border bg-background p-8 hover:shadow-lg transition-all">
                  <div className="flex items-center justify-between mb-4">
                    <span className="inline-block px-3 py-1 text-sm font-medium bg-primary/10 text-primary rounded-full">
                      {guide.framework}
                    </span>
                    {guide.downloadable && (
                      <Download className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  
                  <h3 className="text-xl font-bold mb-3">{guide.title}</h3>
                  <p className="text-muted-foreground mb-4">{guide.description}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-6">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{guide.readTime}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{guide.difficulty}</span>
                    </div>
                  </div>

                  <div className="space-y-2 mb-6">
                    <h4 className="font-semibold text-sm">What you&apos;ll learn:</h4>
                    {guide.topics.slice(0, 3).map((topic, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{topic}</span>
                      </div>
                    ))}
                    {guide.topics.length > 3 && (
                      <div className="text-sm text-muted-foreground">
                        +{guide.topics.length - 3} more topics
                      </div>
                    )}
                  </div>

                  <Button className="w-full" asChild>
                    <Link href={`/resources/guides/${guide.id}`}>
                      {guide.downloadable ? 'Download Guide' : 'Read Guide'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* All Guides */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                All Implementation Guides
              </h2>
              <p className="text-lg text-muted-foreground">
                Browse our complete library of compliance implementation resources
              </p>
            </div>

            {/* Filter Tabs */}
            <div className="flex flex-wrap justify-center gap-2 mb-12">
              <Button variant="outline" size="sm">All Guides</Button>
              {categories.map((category) => (
                <Button key={category} variant="ghost" size="sm">{category}</Button>
              ))}
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {guides.filter(guide => !guide.featured).map((guide) => (
                <div key={guide.id} className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary">
                        <FileText className="h-5 w-5 text-primary-foreground" />
                      </div>
                      <div>
                        <span className="inline-block px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md">
                          {guide.category}
                        </span>
                      </div>
                    </div>
                    {guide.downloadable && (
                      <Download className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  
                  <h3 className="text-lg font-bold mb-2">{guide.title}</h3>
                  <p className="text-muted-foreground mb-4 text-sm">{guide.description}</p>
                  
                  <div className="flex items-center justify-between mb-4">
                    <span className="inline-block px-3 py-1 text-sm font-medium bg-primary/10 text-primary rounded-full">
                      {guide.framework}
                    </span>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{guide.readTime}</span>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/resources/guides/${guide.id}`}>
                      {guide.downloadable ? 'Download' : 'Read Guide'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 text-primary-foreground">
              Need Personalized Implementation Support?
            </h2>
            <p className="text-lg text-primary-foreground/80 mb-8">
              Our compliance experts can provide tailored guidance and hands-on support for your specific implementation needs.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Contact Expert
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/demo">See Platform</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
