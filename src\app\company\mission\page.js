import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Target, Heart, Globe } from "lucide-react";

export default function MissionPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Our <span className="text-primary">Mission</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              To democratize compliance management and make regulatory requirements accessible, 
              automated, and achievable for organizations of all sizes.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
                Transforming Compliance for the Modern World
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                We believe that compliance should be a competitive advantage, not a burden. 
                Our mission is to transform how organizations approach governance, risk, and compliance 
                by providing intelligent, automated solutions that scale with business growth.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Our Core Values
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              The principles that guide everything we do at Auris Compliance.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Target className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Excellence</h3>
              <p className="text-muted-foreground">
                We strive for excellence in everything we do, from product development to customer service, 
                ensuring the highest quality compliance solutions.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Heart className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Customer-Centric</h3>
              <p className="text-muted-foreground">
                Our customers' success is our success. We build solutions that truly solve real-world 
                compliance challenges and deliver measurable value.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Globe className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Innovation</h3>
              <p className="text-muted-foreground">
                We continuously innovate to solve complex compliance challenges with simple, 
                elegant solutions that leverage the latest technology.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Vision */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Our Vision
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed mb-8">
              We envision a world where compliance is no longer a barrier to innovation and growth, 
              but rather an enabler of trust, security, and business success. Through intelligent 
              automation and comprehensive frameworks, we're building the future of compliance management.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Our Mission
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Impact */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Our Impact Goals
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              The positive change we aim to create in the compliance industry.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Democratize Compliance</h3>
              <p className="text-muted-foreground">
                Make enterprise-grade compliance accessible to organizations of all sizes, 
                from startups to large enterprises.
              </p>
            </div>
            
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Reduce Compliance Burden</h3>
              <p className="text-muted-foreground">
                Significantly reduce the time, cost, and complexity associated with 
                achieving and maintaining regulatory compliance.
              </p>
            </div>
            
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Enable Innovation</h3>
              <p className="text-muted-foreground">
                Free organizations to focus on innovation and growth by automating 
                routine compliance tasks and processes.
              </p>
            </div>
            
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Build Trust</h3>
              <p className="text-muted-foreground">
                Help organizations build trust with customers, partners, and stakeholders 
                through transparent and verifiable compliance.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Join Us in Transforming Compliance
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Be part of the compliance revolution. Join our waitlist and help us build 
              the future of regulatory management.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/company/careers">View Careers</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
