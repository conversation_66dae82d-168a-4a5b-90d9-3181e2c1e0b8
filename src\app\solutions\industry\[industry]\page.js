import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Building, Shield, Users } from "lucide-react";
import { notFound } from "next/navigation";

// Industry data
const industryData = {
  "financial-services": {
    name: "Financial Services",
    title: "Solutions for Financial Services",
    description: "Transforming Financial Services Governance, Risk, and Compliance",
    longDescription: "In today's highly regulated and rapidly evolving financial landscape, organizations face mounting pressure to secure assets, manage risk, and maintain compliance across multiple frameworks—all while optimizing operational efficiency. Our AI-powered GRC orchestration platform delivers a unified, automated, and verifiable solution tailored for the unique challenges of financial services.",
    frameworks: ["PCI DSS", "SOX", "GDPR", "POPIA", "ISO 27001", "SOC 2"],
    keyBenefits: [
      {
        title: "Real-Time Compliance & Security Monitoring",
        description: "Instantly visualize your compliance status and security posture with a real-time dashboard, integrating live threat intelligence and compliance data across all frameworks relevant to financial institutions. Receive critical alerts and actionable insights to address vulnerabilities and emerging threats before they impact your business."
      },
      {
        title: "Automated Regulatory Compliance",
        description: "Streamline adherence to financial regulations (PCI DSS, SOX, GDPR, POPIA, and more) with automated policy enforcement, control testing, and evidence collection. Reduce manual workloads and minimize human error by leveraging AI-driven workflows for compliance gap analysis, risk assessments, and audit readiness."
      },
      {
        title: "Blockchain-Secured Asset Management",
        description: "Ensure the integrity and authenticity of your IT, OT, and IoT assets with blockchain-based asset tokenization and tamper-proof configuration histories—critical for financial data protection and audit trails. Instantly verify compliance status and asset configurations with cryptographic proof, supporting regulatory audits and third-party due diligence."
      },
      {
        title: "Advanced Threat & Vulnerability Management",
        description: "Benefit from continuous monitoring, AI-powered anomaly detection, and prioritized alerts for threats targeting the financial sector. Proactively identify and remediate vulnerabilities with automated patch management and zero-day tracking, reducing risk exposure and maintaining trust with clients and regulators."
      },
      {
        title: "Third-Party & Supply Chain Risk Oversight",
        description: "Assess and monitor the security posture of vendors, partners, and service providers with automated third-party risk assessments and vendor scorecards—essential for financial ecosystems with complex supply chains."
      },
      {
        title: "Process Automation & Incident Response",
        description: "Accelerate incident response and remediation with pre-built playbooks, automated workflows, and coordinated task management, ensuring rapid containment and resolution of security events. Leverage AI agents to orchestrate end-to-end compliance and security operations, freeing your team to focus on strategic priorities."
      },
      {
        title: "Comprehensive Reporting & Audit Support",
        description: "Generate custom, framework-specific compliance reports and executive dashboards with a few clicks, simplifying regulatory submissions and stakeholder communications. Securely share audit evidence and compliance documentation via blockchain-backed portals, enabling transparent and efficient external audit processes."
      }
    ],
    whyChoose: [
      "Enterprise-grade GRC capabilities accessible to financial institutions of any size.",
      "Unified, AI-driven compliance across all relevant financial regulations and standards.",
      "Blockchain-secured evidence and asset management for unmatched auditability and trust.",
      "Automated, standardized security operations—no need for large in-house teams.",
      "Rapid deployment, seamless integration with existing financial systems, and ongoing support tailored to the sector's evolving needs."
    ]
  },
  "healthcare": {
    name: "Healthcare",
    title: "Healthcare Compliance Solutions",
    description: "Medical and health tech compliance for healthcare organizations.",
    longDescription: "Specialized compliance solutions for hospitals, clinics, health tech companies, and medical device manufacturers. Ensure patient data protection and regulatory compliance.",
    frameworks: ["HIPAA", "ISO 27001", "SOC 2", "FDA 21 CFR Part 11"],
    challenges: [
      "Patient data protection",
      "Medical device regulations",
      "Clinical trial compliance",
      "Healthcare interoperability"
    ]
  },
  "technology": {
    name: "Technology",
    title: "Technology Company Compliance",
    description: "Tech company compliance frameworks for software and SaaS organizations.",
    longDescription: "Tailored compliance solutions for software companies, SaaS providers, and technology startups. Scale your compliance program as you grow.",
    frameworks: ["SOC 2", "ISO 27001", "GDPR", "CCPA", "PCI DSS"],
    challenges: [
      "Rapid scaling requirements",
      "Customer data protection",
      "International compliance",
      "Vendor risk management"
    ]
  },
  "ecommerce": {
    name: "Ecommerce",
    title: "Ecommerce Compliance Management",
    description: "Online retail compliance management for ecommerce businesses.",
    longDescription: "Comprehensive compliance solutions for online retailers, marketplaces, and ecommerce platforms. Protect customer data and ensure payment security.",
    frameworks: ["PCI DSS", "GDPR", "CCPA", "SOC 2", "ISO 27001"],
    challenges: [
      "Payment card security",
      "Customer data privacy",
      "International regulations",
      "Third-party integrations"
    ]
  },
  "education": {
    name: "Education",
    title: "Educational Institution Compliance",
    description: "Educational institution compliance for schools and universities.",
    longDescription: "Specialized compliance solutions for educational institutions, EdTech companies, and student information systems. Protect student data and ensure educational compliance.",
    frameworks: ["FERPA", "COPPA", "SOC 2", "ISO 27001", "GDPR"],
    challenges: [
      "Student data protection",
      "Educational privacy laws",
      "Research data security",
      "Campus security requirements"
    ]
  },
  "manufacturing": {
    name: "Manufacturing",
    title: "Manufacturing Compliance Solutions",
    description: "Industrial compliance requirements for manufacturing organizations.",
    longDescription: "Comprehensive compliance solutions for manufacturing companies, including quality management, environmental compliance, and industrial security requirements.",
    frameworks: ["ISO 9001", "ISO 14001", "ISO 27001", "SOC 2"],
    challenges: [
      "Quality management systems",
      "Environmental compliance",
      "Supply chain security",
      "Industrial IoT security"
    ]
  }
};

export default async function IndustryPage({ params }) {
  const resolvedParams = await params;
  const industry = industryData[resolvedParams.industry];

  if (!industry) {
    notFound();
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">{industry.name}</span> Compliance Solutions
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              {industry.longDescription}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Supported Frameworks */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Supported Frameworks
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Key compliance frameworks relevant to the {industry.name.toLowerCase()} industry.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
            {industry.frameworks.map((framework, index) => (
              <div key={index} className="rounded-lg border bg-background p-4 text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mx-auto mb-3">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="font-semibold text-sm">{framework}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Key Benefits for Financial Services */}
      {resolvedParams.industry === 'financial-services' && industry.keyBenefits && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                Key Benefits for Financial Services
              </h2>
              <p className="mt-4 text-lg text-muted-foreground">
                Comprehensive solutions designed specifically for the unique challenges of financial institutions.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 lg:gap-12">
              {industry.keyBenefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-primary-foreground text-lg font-bold flex-shrink-0">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-4">{benefit.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Industry Challenges - for other industries */}
      {resolvedParams.industry !== 'financial-services' && industry.challenges && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-2xl text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                Industry Challenges We Address
              </h2>
              <p className="mt-4 text-lg text-muted-foreground">
                Common compliance challenges faced by {industry.name.toLowerCase()} organizations.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {industry.challenges.map((challenge, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{challenge}</h3>
                    <p className="text-muted-foreground">
                      Specialized solutions to address this critical compliance challenge in the {industry.name.toLowerCase()} sector.
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Industry-Specific Features
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Tailored compliance features for {industry.name.toLowerCase()} organizations.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Building className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Industry Templates</h3>
              <p className="text-muted-foreground">
                Pre-configured templates and workflows designed specifically for {industry.name.toLowerCase()} compliance.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Shield className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Regulatory Mapping</h3>
              <p className="text-muted-foreground">
                Automated mapping of controls to industry-specific regulations and standards.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Users className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Expert Support</h3>
              <p className="text-muted-foreground">
                Access to compliance experts with deep {industry.name.toLowerCase()} industry knowledge.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Our Platform - Financial Services */}
      {resolvedParams.industry === 'financial-services' && industry.whyChoose && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                  Why Choose Our Platform for Financial Services?
                </h2>
              </div>

              <div className="space-y-6">
                {industry.whyChoose.map((reason, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-bold flex-shrink-0 mt-1">
                      ✓
                    </div>
                    <p className="text-lg text-muted-foreground leading-relaxed">
                      {reason}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Final CTA - Financial Services */}
      {resolvedParams.industry === 'financial-services' && (
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Transform Your Financial Services GRC
              </h2>
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                Empower your financial organization to stay ahead of regulatory change, mitigate risk, and build trust with clients and regulators—while reducing operational overhead.
              </p>
              <p className="text-lg text-muted-foreground mb-10 leading-relaxed">
                Discover how our GRC orchestration platform can transform your approach to governance, risk, and compliance in financial services.
              </p>
              <div className="flex items-center justify-center gap-x-6">
                <Button size="lg" asChild>
                  <Link href="/waitlist">
                    Join Waitlist
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/demo">See Demo</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Coming Soon - Other Industries */}
      {resolvedParams.industry !== 'financial-services' && (
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                Industry Solutions Coming Soon
              </h2>
              <p className="mt-4 text-lg text-muted-foreground">
                Detailed {industry.name.toLowerCase()} compliance solutions and industry-specific features
                will be available when we launch.
              </p>
              <div className="mt-8">
                <Button size="lg" asChild>
                  <Link href="/waitlist">
                    Join Waitlist
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(industryData).map((industry) => ({
    industry: industry,
  }));
}
