import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Database,
  Zap,
  Settings,
  Eye,
  Shield,
  Search,
  Monitor,
  Activity,
  Lock,
  CheckCircle,
  Globe,
  Cloud,
  Smartphone,
  HardDrive,
  Wifi,
  Server,
  Network,
  Cpu,
  Camera,
  Lightbulb,
  RefreshCw,
  AlertTriangle,
  BarChart3,
  TrendingUp,
  Target,
  Brain,
  FileText,
  Calendar,
  Users,
  Building,
  Key,
  Layers,
  GitBranch,
  Microscope,
  Radar,
  Compass
} from "lucide-react";

export default function LightHousePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              LightHouse: Illuminating Your <span className="text-blue-500">Digital Universe</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              The Foundation That Sees Everything
            </p>
            <p className="mt-4 text-base leading-7 text-muted-foreground max-w-3xl mx-auto">
              You can&apos;t protect what you don&apos;t know exists. LightHouse serves as the foundational layer of GRCOS, providing comprehensive visibility into every IT, OT, and IoT asset in your environment while creating the blockchain-secured foundation that makes all other GRC activities possible.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Explore LightHouse Capabilities
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Schedule Discovery Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Asset Discovery & Inventory: Complete Visibility */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Asset Discovery & Inventory: <span className="text-blue-500">Complete Visibility</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Every Asset, Automatically Discovered</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse continuously scans your entire technology landscape, automatically discovering and cataloging assets across traditional IT infrastructure, operational technology, and the growing Internet of Things ecosystem.
            </p>
          </div>

          {/* Comprehensive Asset Coverage */}
          <div className="rounded-lg border bg-background p-8 mb-12">
            <h3 className="text-xl font-semibold mb-6">Comprehensive Asset Coverage</h3>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Server className="h-6 w-6 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">IT Assets</h4>
                  <p className="text-sm text-muted-foreground">Servers, workstations, network devices, cloud resources, virtual machines</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Cpu className="h-6 w-6 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">OT Assets</h4>
                  <p className="text-sm text-muted-foreground">Industrial control systems, SCADA devices, PLCs, manufacturing equipment</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Smartphone className="h-6 w-6 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">IoT Assets</h4>
                  <p className="text-sm text-muted-foreground">Smart devices, sensors, cameras, building automation systems</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Eye className="h-6 w-6 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Shadow IT</h4>
                  <p className="text-sm text-muted-foreground">Unauthorized applications, rogue devices, cloud services</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Intelligent Discovery Engine */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Search className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Discovery Engine</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Network scanning with minimal performance impact</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Cloud API integration for complete cloud asset visibility</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Agent-based discovery for detailed system information</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Passive network monitoring for stealth asset identification</span>
                </li>
              </ul>
            </div>

            {/* Real-Time Asset Tracking */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Activity className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Real-Time Asset Tracking</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous monitoring for new assets entering the environment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Automatic detection of asset changes and updates</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Asset lifecycle management from deployment to decommissioning</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with existing asset management systems</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Blockchain Tokenization: Cryptographic Trust */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Blockchain Tokenization: <span className="text-blue-500">Cryptographic Trust</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Every Asset, Cryptographically Verified</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse transforms discovered assets into blockchain tokens using Hyperledger Fabric, creating an immutable, verifiable record that forms the foundation of trust for all GRC activities.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Asset Tokenization Process */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Asset Tokenization Process</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Unique cryptographic identity for every discovered asset</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Immutable record of asset discovery and initial configuration</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Tamper-proof history of all asset changes and updates</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Smart contract validation of asset authenticity</span>
                </li>
              </ul>
            </div>

            {/* Trust Benefits */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Trust Benefits</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Audit Confidence:</strong> Auditors can verify asset inventory without questioning authenticity</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Compliance Evidence:</strong> Cryptographic proof of asset management controls</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Change Verification:</strong> Immutable record of what changed, when, and why</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Third-Party Validation:</strong> Enable verification without exposing sensitive details</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Blockchain Integration */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mr-4">
                <GitBranch className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Blockchain Integration</h3>
                <p className="text-sm text-muted-foreground">Enterprise-grade blockchain infrastructure</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Network className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Hyperledger Fabric Network</h4>
                  <p className="text-sm text-muted-foreground">Optimized for enterprise security</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <FileText className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Smart Contracts</h4>
                  <p className="text-sm text-muted-foreground">Automated asset validation</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Users className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Consensus Mechanisms</h4>
                  <p className="text-sm text-muted-foreground">Ensuring data integrity</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Eye className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Privacy-Preserving Verification</h4>
                  <p className="text-sm text-muted-foreground">Capabilities</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Security Monitoring: Always Watching */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Security Monitoring: <span className="text-blue-500">Always Watching</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Real-Time Surveillance with Business Context</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Powered by Wazuh SIEM+XDR, LightHouse provides comprehensive security monitoring that goes beyond traditional alerting to deliver actionable intelligence with business context.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Comprehensive Security Coverage */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Monitor className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Comprehensive Security Coverage</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Endpoint Detection:</strong> Advanced threat detection across all discovered assets</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Network Monitoring:</strong> Traffic analysis and anomaly detection</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Log Aggregation:</strong> Centralized collection and analysis of security events</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Behavioral Analytics:</strong> Machine learning-powered detection of unusual activities</span>
                </li>
              </ul>
            </div>

            {/* Intelligent Alert Management */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Alert Management</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>AI-powered alert prioritization based on business impact</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Contextual enrichment using asset inventory and risk data</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Automated correlation of related security events</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with threat intelligence for enhanced context</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Business-Aware Monitoring */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mr-4">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Business-Aware Monitoring</h3>
                <p className="text-sm text-muted-foreground">Security monitoring aligned with business priorities</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Target className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Asset Criticality Assessment</h4>
                  <p className="text-sm text-muted-foreground">Priority-based monitoring</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Business Process Impact Analysis</h4>
                  <p className="text-sm text-muted-foreground">For security events</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Compliance Requirement Mapping</h4>
                  <p className="text-sm text-muted-foreground">Regulatory alert prioritization</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Executive Dashboard Integration</h4>
                  <p className="text-sm text-muted-foreground">Strategic security visibility</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Evidence Collection: Automated Compliance */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Evidence Collection: <span className="text-blue-500">Automated Compliance</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Continuous Evidence Generation</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse automatically collects compliance evidence as part of normal operations, eliminating the scramble for documentation during audit periods.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Automated Evidence Types */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Automated Evidence Types</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Asset Inventories:</strong> Complete, real-time asset listings with blockchain verification</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Configuration Baselines:</strong> Secure configuration standards with deviation tracking</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Security Event Logs:</strong> Comprehensive audit trails with tamper-proof storage</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Vulnerability Data:</strong> Continuous scanning results with remediation tracking</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Access Control Evidence:</strong> Real-time monitoring of system access and privileges</span>
                </li>
              </ul>
            </div>

            {/* Compliance Framework Alignment */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Layers className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Compliance Framework Alignment</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Evidence collection mapped to specific framework requirements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Automated gap identification for incomplete evidence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Timeline tracking for evidence freshness requirements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Cross-framework evidence correlation for efficient multi-standard compliance</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Blockchain Evidence Storage */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mr-4">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Blockchain Evidence Storage</h3>
                <p className="text-sm text-muted-foreground">Immutable, verifiable evidence management</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Cryptographic Verification</h4>
                  <p className="text-sm text-muted-foreground">Of all collected evidence</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Immutable Timestamps</h4>
                  <p className="text-sm text-muted-foreground">For audit trail integrity</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Smart Contract Validation</h4>
                  <p className="text-sm text-muted-foreground">Of evidence completeness</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Eye className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Zero-Knowledge Proofs</h4>
                  <p className="text-sm text-muted-foreground">Privacy-preserving verification</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Threat Intelligence: Contextual Awareness */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Threat Intelligence: <span className="text-blue-500">Contextual Awareness</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Intelligence That Informs Action</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse integrates threat intelligence from multiple sources, providing context-aware security monitoring that understands both global threats and your specific environment.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Threat Intelligence Sources */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Radar className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Threat Intelligence Sources</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>MISP Integration:</strong> Malware Information Sharing Platform for community intelligence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>OpenCTI:</strong> Structured threat intelligence with relationship mapping</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Commercial Feeds:</strong> Premium threat intelligence for enhanced coverage</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Internal Intelligence:</strong> Organization-specific threat patterns and indicators</span>
                </li>
              </ul>
            </div>

            {/* Contextual Analysis */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Microscope className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Contextual Analysis</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Asset-specific threat assessment based on discovered vulnerabilities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Business process risk evaluation using threat intelligence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Geographic threat correlation for location-specific risks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Industry-specific threat pattern analysis</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Actionable Intelligence */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mr-4">
                <Target className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Actionable Intelligence</h3>
                <p className="text-sm text-muted-foreground">Intelligence that drives immediate action</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Zap className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Automated IOC Deployment</h4>
                  <p className="text-sm text-muted-foreground">To security tools</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Search className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Threat Hunting Playbooks</h4>
                  <p className="text-sm text-muted-foreground">Based on current intelligence</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Risk-Based Vulnerability Prioritization</h4>
                  <p className="text-sm text-muted-foreground">Using threat context</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Users className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Executive Briefings</h4>
                  <p className="text-sm text-muted-foreground">On relevant threat landscape changes</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Continuous Monitoring: Never-Ending Vigilance */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Continuous Monitoring: <span className="text-blue-500">Never-Ending Vigilance</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">24/7 Security Posture Management</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse provides continuous monitoring capabilities that ensure your security posture remains strong around the clock, with intelligent anomaly detection and automated response capabilities.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Anomaly Detection Engine */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Anomaly Detection Engine</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Machine learning models trained on your environment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Behavioral baselines for users, devices, and applications</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Statistical analysis of security metrics and trends</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Predictive analytics for emerging security risks</span>
                </li>
              </ul>
            </div>

            {/* Automated Response Capabilities */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <RefreshCw className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Automated Response Capabilities</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with Action Centre for orchestrated incident response</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Automated containment of suspicious activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Dynamic policy adjustment based on threat levels</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Escalation workflows for human intervention when needed</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Performance Monitoring */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mr-4">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Performance Monitoring</h3>
                <p className="text-sm text-muted-foreground">Continuous assessment of security effectiveness</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Settings className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Security Tool Effectiveness</h4>
                  <p className="text-sm text-muted-foreground">Measurement</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Activity className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Control Performance</h4>
                  <p className="text-sm text-muted-foreground">Analytics</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Compliance Posture</h4>
                  <p className="text-sm text-muted-foreground">Trending</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Risk Metric Tracking</h4>
                  <p className="text-sm text-muted-foreground">And reporting</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Integration Excellence */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Technology Integration <span className="text-blue-500">Excellence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Connected Security Ecosystem</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse serves as the integration hub for your entire security ecosystem, normalizing data and providing unified visibility across disparate tools.
            </p>
          </div>

          {/* Core Technology Stack */}
          <div className="rounded-lg border bg-background p-8 mb-12">
            <h3 className="text-xl font-semibold mb-6">Core Technology Stack</h3>
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {/* Data Management */}
              <div>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-4">
                  <Database className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold mb-3">Data Management</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>DataGerry:</strong> Foundational CMDB for comprehensive asset relationships</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Apache Kafka:</strong> Real-time data streaming for component integration</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Amazon Managed Apache Airflow:</strong> Data pipeline orchestration and normalization</span>
                  </li>
                </ul>
              </div>

              {/* Security Monitoring */}
              <div>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-4">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold mb-3">Security Monitoring</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Wazuh SIEM+XDR:</strong> Comprehensive security event management</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Custom API Connectors:</strong> Integration with existing security tools</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Data Normalization Pipeline:</strong> Consistent data formats across all sources</span>
                  </li>
                </ul>
              </div>

              {/* Asset Discovery */}
              <div>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-4">
                  <Search className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold mb-3">Asset Discovery</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Network Scanning Engines:</strong> Comprehensive asset discovery capabilities</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Cloud API Integration:</strong> Native cloud asset management</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Agent-Based Discovery:</strong> Detailed system configuration collection</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span><strong>Passive Network Monitoring:</strong> Stealth asset identification</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Data Normalization: Unified Intelligence */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mr-4">
                <Layers className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Data Normalization: Unified Intelligence</h3>
                <p className="text-sm text-muted-foreground">One Source of Truth</p>
              </div>
            </div>
            <p className="text-muted-foreground mb-6">
              LightHouse transforms data chaos into actionable intelligence through sophisticated data normalization that creates a single, consistent view of your security posture.
            </p>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h4 className="font-semibold mb-3">Data Standardization</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Common data models across all security tools</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Standardized asset classification and tagging</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Unified vulnerability scoring and prioritization</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Consistent risk metrics and calculations</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Integration Benefits</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Eliminate data silos between security tools</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Reduce false positives through correlation</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Enable comprehensive risk analysis across all data sources</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                    <span>Simplify reporting and dashboard creation</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* GRCOS Module Integration */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              GRCOS Module <span className="text-blue-500">Integration</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">The Foundation That Powers Everything</h3>
            <p className="text-lg text-muted-foreground mb-8">
              As the foundational layer of GRCOS, LightHouse provides essential data and capabilities to all other modules.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* ComplianceCentre Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">ComplianceCentre Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Asset inventory for framework scope definition</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Security monitoring data for control effectiveness assessment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Evidence collection for compliance verification</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Risk data for compliance prioritization</span>
                </li>
              </ul>
            </div>

            {/* Action Centre Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Action Centre Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time security events triggering automated workflows</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Asset data for targeted remediation activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Threat intelligence for informed incident response</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Evidence collection for remediation verification</span>
                </li>
              </ul>
            </div>

            {/* Trust Centre Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Trust Centre Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Blockchain-verified evidence for stakeholder confidence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Security monitoring data for transparent reporting</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Asset inventory for audit scope definition</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Threat intelligence for risk communication</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* The LightHouse Advantage */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              The LightHouse <span className="text-blue-500">Advantage</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Why Foundation Matters</h3>
            <p className="text-lg text-muted-foreground mb-8">
              A strong GRC program requires a strong foundation. LightHouse provides the visibility, verification, and intelligence that makes everything else possible.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Complete Visibility */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Complete Visibility</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>100% asset discovery across IT, OT, and IoT environments</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time monitoring of security posture</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Comprehensive evidence collection for compliance</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Contextual threat intelligence for informed decisions</span>
                </li>
              </ul>
            </div>

            {/* Cryptographic Trust */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Cryptographic Trust</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Blockchain verification of all asset data</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Immutable audit trails for compliance confidence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Tamper-proof evidence collection</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Third-party verification capabilities</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Intelligent Operations */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Operations</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>AI-powered anomaly detection and alerting</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Automated evidence collection and organization</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Business-context security monitoring</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Predictive risk analysis and mitigation</span>
                </li>
              </ul>
            </div>

            {/* Scalable Architecture */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Globe className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Scalable Architecture</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Cloud-native design that grows with your business</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>API-first integration with existing tools</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Modular deployment for flexible implementation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Performance optimization for enterprise scale</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Measurable Foundation Strength */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Measurable <span className="text-blue-500">Foundation Strength</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Metrics That Matter</h3>
            <p className="text-lg text-muted-foreground mb-8">
              LightHouse provides clear visibility into the effectiveness of your foundational security capabilities:
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Asset Management Metrics */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Database className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Asset Management Metrics</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>99.5%</strong> asset discovery accuracy across all environments</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time asset inventory updates within <strong>5 minutes</strong></span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>100%</strong> blockchain verification of asset records</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>95%</strong> reduction in unknown asset security incidents</span>
                </li>
              </ul>
            </div>

            {/* Security Monitoring Metrics */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <Monitor className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Security Monitoring Metrics</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>85%</strong> reduction in false positive security alerts</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>70%</strong> faster threat detection through intelligent correlation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>90%</strong> improvement in security event contextualization</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>60%</strong> reduction in mean time to threat identification</span>
                </li>
              </ul>
            </div>

            {/* Evidence Collection Metrics */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 mb-6">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Evidence Collection Metrics</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>100%</strong> automated evidence collection for supported frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>99%</strong> evidence integrity verification through blockchain</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>80%</strong> reduction in audit preparation time</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span><strong>95%</strong> auditor satisfaction with evidence quality and accessibility</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Ready to Illuminate Your Security Universe? */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Ready to Illuminate Your <span className="text-blue-500">Security Universe?</span>
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Transform your unknown digital landscape into a comprehensively monitored, blockchain-verified foundation for enterprise-grade GRC.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Explore LightHouse Capabilities
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Schedule Discovery Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
