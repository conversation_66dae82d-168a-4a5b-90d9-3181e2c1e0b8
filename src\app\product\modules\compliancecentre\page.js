import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Shield,
  CheckCircle,
  FileText,
  BarChart3,
  Brain,
  Database,
  Settings,
  Search,
  TrendingUp,
  Globe,
  Lock,
  Eye,
  Users,
  Target,
  Layers,
  Workflow,
  Activity,
  RefreshCw,
  Zap,
  Code,
  GitBranch,
  Monitor,
  AlertTriangle,
  Calendar,
  Download,
  Building,
  Key,
  Cloud,
  Smartphone,
  HardDrive,
  Wifi,
  BookOpen,
  PieChart,
  LineChart,
  Map,
  Compass,
  Microscope
} from "lucide-react";

export default function ComplianceCentrePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Compliance Centre: The Analytical Core of <span className="text-green-500">GRC Excellence</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Intelligence That Transforms Compliance
            </p>
            <p className="mt-4 text-base leading-7 text-muted-foreground max-w-3xl mx-auto">
              Compliance isn&apos;t about checking boxes—it&apos;s about making intelligent decisions that protect your business while meeting regulatory requirements. The GRCOS Compliance Centre serves as your analytical powerhouse, transforming complex frameworks into actionable insights through AI-powered analysis and OSCAL standardization.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Explore Compliance Analytics
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Schedule Intelligence Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Framework Management: Multi-Standard Mastery */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Framework Management: <span className="text-green-500">Multi-Standard Mastery</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">One Platform, Every Framework</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Compliance Centre manages multiple compliance frameworks through a unified approach that eliminates duplicate efforts while ensuring comprehensive coverage across all regulatory requirements.
            </p>
          </div>

          {/* Supported Frameworks with OSCAL Integration */}
          <div className="rounded-lg border bg-background p-8 mb-12">
            <h3 className="text-xl font-semibold mb-6">Supported Frameworks with OSCAL Integration</h3>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex items-start space-x-3">
                <Shield className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">NIST Cybersecurity Framework 2.0</h4>
                  <p className="text-sm text-muted-foreground">Complete implementation with AI-powered gap analysis</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Lock className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">ISO 27001:2022</h4>
                  <p className="text-sm text-muted-foreground">Information security management with intelligent control mapping</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">SOC 2 Type II</h4>
                  <p className="text-sm text-muted-foreground">Service organization controls with automated evidence collection</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Database className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">PCI DSS v4.0</h4>
                  <p className="text-sm text-muted-foreground">Payment card security with continuous compliance monitoring</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Users className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">HIPAA Security Rule</h4>
                  <p className="text-sm text-muted-foreground">Healthcare privacy protection with risk-based controls</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Framework Intelligence Features */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Framework Intelligence Features</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Automated Import:</strong> OSCAL-formatted framework ingestion with validation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Cross-Framework Mapping:</strong> Intelligent correlation of controls across standards</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Gap Analysis:</strong> AI-powered identification of compliance gaps and overlaps</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Update Management:</strong> Automatic framework updates with impact analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Custom Framework Support:</strong> Ability to import organization-specific requirements</span>
                </li>
              </ul>
            </div>

            {/* OSCAL Standardization Benefits */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Code className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">OSCAL Standardization Benefits</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Machine-readable framework representation for automated processing</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Standardized control definitions across all supported frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Interoperable compliance data that works with any OSCAL-compatible tool</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Future-proof compliance management as new frameworks adopt OSCAL</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Policy Development: AI-Powered Policy Creation */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Policy Development: <span className="text-green-500">AI-Powered Policy Creation</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">From Requirements to Reality</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Transform regulatory requirements into actionable organizational policies with AI assistance that understands both compliance mandates and business context.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Intelligent Policy Generation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Policy Generation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Natural Language Processing:</strong> Convert complex regulatory text into clear policy language</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Business Context Integration:</strong> Policies tailored to your specific industry and operations</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Template Library:</strong> Pre-built policy templates for common compliance scenarios</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Version Control:</strong> Comprehensive policy lifecycle management with approval workflows</span>
                </li>
              </ul>
            </div>

            {/* AI-Assisted Features */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">AI-Assisted Features</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Gap Identification:</strong> Automatic detection of policy coverage gaps</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Consistency Checking:</strong> Ensure policy alignment across frameworks and business units</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Impact Assessment:</strong> Predict business impact of proposed policy changes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Optimization Recommendations:</strong> Suggestions for policy efficiency improvements</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Policy Development Workflow */}
          <div className="rounded-lg border bg-background p-8">
            <h3 className="text-xl font-semibold mb-6">Policy Development Workflow</h3>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">1</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Framework Analysis</h4>
                  <p className="text-sm text-muted-foreground">Framework requirement analysis and business context integration</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">2</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">AI Generation</h4>
                  <p className="text-sm text-muted-foreground">AI-generated policy drafts with stakeholder-specific language</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">3</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Collaborative Review</h4>
                  <p className="text-sm text-muted-foreground">Collaborative review and approval process with version tracking</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">4</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Deployment & Monitoring</h4>
                  <p className="text-sm text-muted-foreground">Automated policy distribution and acknowledgment management</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Control Management: Comprehensive Security Controls */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Control Management: <span className="text-green-500">Comprehensive Security Controls</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Controls That Actually Control</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Move beyond theoretical controls to implementable, measurable security measures that provide real protection while satisfying compliance requirements.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Intelligent Control Design */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Control Design</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>AI-Powered Control Creation:</strong> Generate controls based on risk assessment and framework requirements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Implementation Guidance:</strong> Step-by-step instructions for control deployment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Effectiveness Measurement:</strong> Quantitative metrics for control performance</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Continuous Improvement:</strong> AI-driven recommendations for control optimization</span>
                </li>
              </ul>
            </div>

            {/* Control Library Features */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Database className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Control Library Features</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>OWASP SCF Integration:</strong> Comprehensive security control framework mapping</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Custom Control Development:</strong> Create organization-specific controls with AI assistance</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Control Families:</strong> Logical grouping of related controls for efficient management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Maturity Assessment:</strong> Evaluate control implementation maturity with improvement roadmaps</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Control Lifecycle Management */}
          <div className="rounded-lg border bg-background p-8">
            <h3 className="text-xl font-semibold mb-6">Control Lifecycle Management</h3>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">1</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Design</h4>
                  <p className="text-sm text-muted-foreground">Design and specification with business impact analysis</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">2</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Implementation</h4>
                  <p className="text-sm text-muted-foreground">Implementation planning with resource allocation and timeline management</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">3</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Testing</h4>
                  <p className="text-sm text-muted-foreground">Testing and validation with automated verification capabilities</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">4</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Monitoring</h4>
                  <p className="text-sm text-muted-foreground">Monitoring and maintenance with performance metrics and improvement tracking</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500 text-white text-sm font-semibold">5</div>
                <div>
                  <h4 className="font-semibold text-sm mb-1">Retirement</h4>
                  <p className="text-sm text-muted-foreground">Retirement and replacement with impact assessment and migration planning</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Assessment Management: Comprehensive Risk Evaluation */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Assessment Management: <span className="text-green-500">Comprehensive Risk Evaluation</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Assessments That Drive Decisions</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Conduct thorough risk assessments that combine quantitative analysis with qualitative insights, providing the intelligence needed for informed security investment decisions.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 mb-12">
            {/* Risk Assessments */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Risk Assessments</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Quantitative Analysis:</strong> Statistical modeling using Open-Source Risk Engine (ORE)</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Qualitative Evaluation:</strong> Expert judgment integration with AI-powered analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Business Impact Modeling:</strong> Financial and operational impact assessment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Threat Landscape Integration:</strong> Current threat intelligence incorporated into risk calculations</span>
                </li>
              </ul>
            </div>

            {/* Control Assessments */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Control Assessments</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Design Effectiveness:</strong> Evaluate whether controls can achieve intended outcomes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Operating Effectiveness:</strong> Assess whether controls are functioning as designed</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Maturity Evaluation:</strong> Benchmark control sophistication against industry standards</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Gap Analysis:</strong> Identify control weaknesses and improvement opportunities</span>
                </li>
              </ul>
            </div>

            {/* Vendor Risk Assessments */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Vendor Risk Assessments</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Third-Party Security Evaluation:</strong> Comprehensive vendor security posture analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Supply Chain Risk Analysis:</strong> Multi-tier vendor risk assessment and management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Contractual Security Review:</strong> Automated analysis of vendor security commitments</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Continuous Vendor Monitoring:</strong> Ongoing assessment of vendor security changes</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Assessment Automation Features */}
          <div className="rounded-lg border bg-background p-8">
            <h3 className="text-xl font-semibold mb-6">Assessment Automation Features</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Database className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Automated Data Collection</h4>
                  <p className="text-sm text-muted-foreground">Integration with LightHouse for real-time assessment data</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Brain className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">AI-Powered Analysis</h4>
                  <p className="text-sm text-muted-foreground">Machine learning models for pattern recognition and trend analysis</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Predictive Risk Modeling</h4>
                  <p className="text-sm text-muted-foreground">Forecast future risk scenarios based on current trends</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Benchmarking</h4>
                  <p className="text-sm text-muted-foreground">Compare risk posture against industry peers and best practices</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Control Mapping: Cross-Framework Intelligence */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Control Mapping: <span className="text-green-500">Cross-Framework Intelligence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Eliminate Duplicate Efforts</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Understand how security controls map across multiple frameworks, enabling efficient implementation that satisfies multiple compliance requirements simultaneously.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Intelligent Mapping Engine */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Map className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Mapping Engine</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Automated Control Correlation:</strong> AI-powered identification of control relationships</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Gap Visualization:</strong> Clear representation of control coverage across frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Optimization Recommendations:</strong> Suggestions for maximizing control effectiveness across standards</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Coverage Analysis:</strong> Detailed assessment of compliance coverage by control</span>
                </li>
              </ul>
            </div>

            {/* Multi-Framework Benefits */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Multi-Framework Benefits</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Efficient Implementation:</strong> Deploy controls once, satisfy multiple frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Cost Optimization:</strong> Reduce compliance costs through shared control implementation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk Reduction:</strong> Comprehensive control coverage through intelligent mapping</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Audit Preparation:</strong> Streamlined evidence collection across multiple standards</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Mapping Visualization */}
          <div className="rounded-lg border bg-background p-8">
            <h3 className="text-xl font-semibold mb-6">Mapping Visualization</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <GitBranch className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Interactive Control Relationships</h4>
                  <p className="text-sm text-muted-foreground">Interactive control relationship diagrams</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <PieChart className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Framework Coverage Heat Maps</h4>
                  <p className="text-sm text-muted-foreground">Framework coverage heat maps</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Gap Analysis Dashboards</h4>
                  <p className="text-sm text-muted-foreground">Gap analysis dashboards with prioritized recommendations</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Compass className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Implementation Roadmaps</h4>
                  <p className="text-sm text-muted-foreground">Implementation roadmaps for multi-framework compliance</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Program Management: Strategic Compliance Oversight */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Program Management: <span className="text-green-500">Strategic Compliance Oversight</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Orchestrated Compliance Excellence</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Manage your entire compliance program with strategic oversight that ensures all activities align with business objectives and regulatory requirements.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Program Planning and Execution */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Program Planning and Execution</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Compliance Roadmaps:</strong> Strategic planning for achieving certification goals</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Resource Allocation:</strong> Optimize staffing and budget across compliance activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Timeline Management:</strong> Coordinate compliance activities with business objectives</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Milestone Tracking:</strong> Monitor progress toward compliance goals with predictive analytics</span>
                </li>
              </ul>
            </div>

            {/* Stakeholder Management */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Stakeholder Management</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Executive Reporting:</strong> Strategic compliance insights for leadership decision-making</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Board Communication:</strong> Governance-ready reporting on compliance program status</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Audit Coordination:</strong> Streamlined management of external audit activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Vendor Relationships:</strong> Coordinated management of compliance-related vendor relationships</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Continuous Improvement */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mr-4">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Continuous Improvement</h3>
                <p className="text-sm text-muted-foreground">Data-driven program optimization</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Program Effectiveness Metrics</h4>
                  <p className="text-sm text-muted-foreground">Quantitative measurement of compliance program value</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Target className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Benchmarking Analysis</h4>
                  <p className="text-sm text-muted-foreground">Compare program maturity against industry standards</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">ROI Analysis</h4>
                  <p className="text-sm text-muted-foreground">Demonstrate business value of compliance investments</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Brain className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Optimization Recommendations</h4>
                  <p className="text-sm text-muted-foreground">AI-powered suggestions for program improvement</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Analytics: Intelligence-Driven Compliance */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Advanced Analytics: <span className="text-green-500">Intelligence-Driven Compliance</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Data Science for Compliance Excellence</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Leverage advanced analytics and machine learning to transform compliance data into actionable business intelligence.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Predictive Analytics */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Predictive Analytics</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk Forecasting:</strong> Predict future risk scenarios based on current trends and threat intelligence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Compliance Drift:</strong> Early warning system for declining compliance posture</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Resource Planning:</strong> Forecast compliance resource requirements based on business growth</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Audit Outcomes:</strong> Predict audit results and prepare proactive responses</span>
                </li>
              </ul>
            </div>

            {/* Statistical Analysis */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Statistical Analysis</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>pandas/scikit-learn Integration:</strong> Advanced data analysis capabilities for compliance metrics</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Trend Analysis:</strong> Statistical modeling of compliance performance over time</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Correlation Analysis:</strong> Identify relationships between controls, risks, and business outcomes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Benchmarking Studies:</strong> Statistical comparison with industry peers and standards</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Machine Learning Applications */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mr-4">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Machine Learning Applications</h3>
                <p className="text-sm text-muted-foreground">AI-powered insights for compliance optimization</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Search className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Anomaly Detection</h4>
                  <p className="text-sm text-muted-foreground">Identify unusual patterns in compliance data that require investigation</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Eye className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Pattern Recognition</h4>
                  <p className="text-sm text-muted-foreground">Discover hidden relationships in complex compliance datasets</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Target className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Optimization Modeling</h4>
                  <p className="text-sm text-muted-foreground">Mathematical optimization of compliance program effectiveness</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BookOpen className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Natural Language Processing</h4>
                  <p className="text-sm text-muted-foreground">Automated analysis of regulatory text and policy documents</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Excellence: Built for Performance */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Technology Excellence: <span className="text-green-500">Built for Performance</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Enterprise-Grade Analytics Platform</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Compliance Centre leverages best-in-class technologies to deliver enterprise-grade analytics capabilities accessible to organizations of any size.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Core Technology Stack */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Code className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Core Technology Stack</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>OSCAL Framework:</strong> Machine-readable compliance standards for automation and interoperability</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Open Policy Agent:</strong> Policy testing and validation for technical control implementation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>OWASP SCF:</strong> Comprehensive security control framework for cross-standard mapping</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Open-Source Risk Engine (ORE):</strong> Quantitative risk analysis and modeling capabilities</span>
                </li>
              </ul>
            </div>

            {/* Vulnerability Management Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Vulnerability Management Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>ArcherySec Platform:</strong> Centralized vulnerability management across multiple scanners</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk-Based Prioritization:</strong> Vulnerability prioritization based on business impact and threat landscape</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Automated Scanning Orchestration:</strong> Coordinated vulnerability assessment across all assets</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Remediation Tracking:</strong> Integration with Action Centre for coordinated vulnerability remediation</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Analytics and Reporting */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mr-4">
                <LineChart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Analytics and Reporting</h3>
                <p className="text-sm text-muted-foreground">Professional-grade data analysis and visualization</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Database className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">pandas Data Analysis</h4>
                  <p className="text-sm text-muted-foreground">Professional-grade data manipulation and analysis</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Brain className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">scikit-learn Machine Learning</h4>
                  <p className="text-sm text-muted-foreground">Advanced analytics for predictive compliance insights</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Monitor className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Custom Dashboard Development</h4>
                  <p className="text-sm text-muted-foreground">Tailored analytics interfaces for specific stakeholder needs</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Automated Report Generation</h4>
                  <p className="text-sm text-muted-foreground">Scheduled delivery of compliance insights to stakeholders</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Excellence: Connected Intelligence */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Integration Excellence: <span className="text-green-500">Connected Intelligence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Seamless Data Flow Across GRC Operations</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Compliance Centre operates as the analytical hub of GRCOS, processing data from all other modules to provide comprehensive compliance intelligence.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* GRCOS Module Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Database className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">GRCOS Module Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>LightHouse Data:</strong> Asset inventory and security monitoring data for comprehensive risk assessment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Action Centre Coordination:</strong> Policy enforcement and remediation data for control effectiveness measurement</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Trust Centre Reporting:</strong> Compliance insights transformed into stakeholder-ready communications</span>
                </li>
              </ul>
            </div>

            {/* External System Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Globe className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">External System Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk Management Platforms:</strong> Bi-directional integration with enterprise risk management systems</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Audit Management Tools:</strong> Seamless data exchange with audit preparation and management platforms</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Business Intelligence Systems:</strong> Compliance metrics integration with corporate dashboards and reporting</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Identity and Access Management:</strong> User and privilege data for access control compliance assessment</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* The Analytical Advantage */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              The <span className="text-green-500">Analytical Advantage</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Why Intelligence Matters in Compliance</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Modern compliance requires more than process—it requires intelligence. Compliance Centre provides the analytical capabilities that transform compliance from cost center to competitive advantage.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Strategic Benefits */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Strategic Benefits</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk-Based Decision Making:</strong> Data-driven prioritization of security investments</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Predictive Compliance:</strong> Anticipate and prevent compliance issues before they occur</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Optimization Insights:</strong> Maximize compliance program effectiveness while minimizing costs</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Stakeholder Confidence:</strong> Demonstrate compliance program value through quantitative analysis</span>
                </li>
              </ul>
            </div>

            {/* Operational Excellence */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Operational Excellence</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Automated Analysis:</strong> Reduce manual effort in compliance assessment and reporting</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Consistent Methodology:</strong> Standardized approaches to risk and compliance analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Comprehensive Coverage:</strong> Unified analysis across all frameworks and business units</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Continuous Improvement:</strong> Ongoing optimization based on performance analytics</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Competitive Advantage */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mr-4">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Competitive Advantage</h3>
                <p className="text-sm text-muted-foreground">Intelligence-driven compliance leadership</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Zap className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Faster Time-to-Compliance</h4>
                  <p className="text-sm text-muted-foreground">Accelerated achievement of certification goals</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Lower Compliance Costs</h4>
                  <p className="text-sm text-muted-foreground">Optimized resource allocation and shared control implementation</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Superior Risk Management</h4>
                  <p className="text-sm text-muted-foreground">Advanced analytics for better risk-based decision making</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Users className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Stakeholder Trust</h4>
                  <p className="text-sm text-muted-foreground">Quantitative demonstration of compliance program effectiveness</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Measurable Analytical Excellence */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Measurable <span className="text-green-500">Analytical Excellence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Metrics That Demonstrate Value</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Compliance Centre provides clear measurement of analytical capabilities and compliance program effectiveness:
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Analysis Efficiency */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Analysis Efficiency</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>90%</strong> reduction in compliance gap analysis time through AI automation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>85%</strong> improvement in risk assessment accuracy using quantitative modeling</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>75%</strong> faster policy development through AI-assisted creation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>95%</strong> consistency in control implementation across frameworks</span>
                </li>
              </ul>
            </div>

            {/* Program Effectiveness */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Program Effectiveness</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>80%</strong> reduction in compliance program costs through optimized control mapping</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>70%</strong> improvement in audit outcomes through predictive preparation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>60%</strong> faster achievement of certification goals through strategic roadmapping</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>90%</strong> stakeholder satisfaction with compliance reporting and insights</span>
                </li>
              </ul>
            </div>

            {/* Risk Management */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500 mb-6">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Risk Management</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>85%</strong> improvement in risk prediction accuracy through advanced analytics</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>75%</strong> reduction in security incidents through proactive risk management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>90%</strong> accuracy in vendor risk assessment through automated analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span><strong>95%</strong> coverage of regulatory requirements through comprehensive framework management</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Ready to Transform Compliance Into Intelligence? */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Ready to Transform Compliance Into <span className="text-green-500">Intelligence?</span>
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Experience how advanced analytics and AI-powered insights transform compliance from reactive obligation into proactive business advantage.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Explore Compliance Analytics
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Schedule Intelligence Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
