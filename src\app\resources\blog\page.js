import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Calendar, User, Tag } from "lucide-react";

export default function BlogPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Compliance <span className="text-primary">Insights</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Stay informed with the latest compliance trends, best practices, and regulatory updates 
              from our team of experts.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center space-x-4 mb-4">
                <span className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                  Featured
                </span>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 mr-1" />
                  Coming Soon
                </div>
              </div>
              <h2 className="text-2xl font-bold mb-4">
                The Future of Compliance Automation: Trends to Watch in 2024
              </h2>
              <p className="text-muted-foreground mb-6">
                Explore the latest trends in compliance automation and how AI and machine learning 
                are transforming regulatory management for modern businesses.
              </p>
              <Button asChild>
                <Link href="/waitlist">
                  Join Waitlist for Updates
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Categories */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Blog Categories
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Explore compliance topics organized by category.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[
              { name: "Compliance Frameworks", count: "Coming Soon", description: "ISO 27001, SOC 2, HIPAA, and more" },
              { name: "Industry Insights", count: "Coming Soon", description: "Sector-specific compliance guidance" },
              { name: "Best Practices", count: "Coming Soon", description: "Proven strategies and methodologies" },
              { name: "Regulatory Updates", count: "Coming Soon", description: "Latest changes in compliance requirements" },
              { name: "Technology Trends", count: "Coming Soon", description: "Automation and compliance tech" },
              { name: "Case Studies", count: "Coming Soon", description: "Real-world implementation stories" }
            ].map((category, index) => (
              <div key={index} className="p-6 rounded-lg border hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">{category.name}</h3>
                  <span className="text-sm text-muted-foreground">{category.count}</span>
                </div>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Upcoming Content */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Upcoming Content
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Blog posts and resources we&apos;re preparing for launch.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {[
              {
                title: "Complete Guide to ISO 27001 Implementation",
                description: "Step-by-step guide for implementing ISO 27001 in your organization",
                category: "Implementation Guide"
              },
              {
                title: "SOC 2 vs ISO 27001: Which is Right for Your Business?",
                description: "Comprehensive comparison of two popular security frameworks",
                category: "Framework Comparison"
              },
              {
                title: "HIPAA Compliance for Health Tech Startups",
                description: "Essential HIPAA requirements for emerging healthcare technology companies",
                category: "Industry Guide"
              },
              {
                title: "Automating Compliance: ROI and Business Benefits",
                description: "How compliance automation delivers measurable business value",
                category: "Business Case"
              }
            ].map((post, index) => (
              <div key={index} className="p-6 rounded-lg border bg-background">
                <div className="flex items-center space-x-2 mb-3">
                  <Tag className="h-4 w-4 text-primary" />
                  <span className="text-sm text-primary font-medium">{post.category}</span>
                </div>
                <h3 className="font-semibold mb-2">{post.title}</h3>
                <p className="text-sm text-muted-foreground">{post.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Stay Updated
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Join our waitlist to be notified when we publish new compliance insights and resources.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist for Blog Updates
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
