import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Database,
  Shield,
  Lock,
  CheckCircle,
  GitBranch,
  Key,
  FileText,
  Calendar,
  Eye,
  Network,
  Layers,
  Zap,
  Target,
  Brain,
  Settings,
  Activity,
  Monitor
} from "lucide-react";

export default function BlockchainEnhancedCMDBPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Blockchain-Enhanced <span className="text-primary">CMDB</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Cryptographic verification of all compliance artifacts
            </p>
            <p className="mt-4 text-base leading-7 text-muted-foreground max-w-3xl mx-auto">
              Transform your Configuration Management Database with blockchain technology to create 
              an immutable, verifiable record of all compliance artifacts, ensuring data integrity 
              and providing cryptographic proof for auditors and stakeholders.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Key Features
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Lock className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Cryptographic Verification</h3>
                <p className="text-muted-foreground">
                  Every compliance artifact is cryptographically signed and stored on the blockchain, 
                  providing tamper-proof evidence of authenticity and integrity.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Database className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Immutable Audit Trail</h3>
                <p className="text-muted-foreground">
                  Complete history of all changes, configurations, and compliance activities 
                  stored in an immutable blockchain ledger for perfect audit trails.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Smart Contract Validation</h3>
                <p className="text-muted-foreground">
                  Automated validation of compliance requirements through smart contracts 
                  that ensure data consistency and policy adherence.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Eye className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Real-time Verification</h3>
                <p className="text-muted-foreground">
                  Instant verification of compliance artifacts and configurations with 
                  real-time blockchain validation for immediate trust assurance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blockchain Architecture */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Enterprise Blockchain Architecture
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Built on Hyperledger Fabric for enterprise-grade security, scalability, and privacy, 
              our blockchain-enhanced CMDB provides the foundation for trustworthy compliance management.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Hyperledger Fabric Foundation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Network className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Hyperledger Fabric Foundation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Permissioned blockchain network for enterprise security</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Modular architecture for flexible deployment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>High-performance transaction processing</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Privacy-preserving data sharing capabilities</span>
                </li>
              </ul>
            </div>

            {/* Smart Contract Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <FileText className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Smart Contract Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Automated compliance rule enforcement</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Self-executing validation workflows</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Consensus-based approval processes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Automated evidence collection triggers</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Cryptographic Features */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mr-4">
                <Key className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Advanced Cryptographic Features</h3>
                <p className="text-sm text-muted-foreground">Enterprise-grade security and verification</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Lock className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Digital Signatures</h4>
                  <p className="text-sm text-muted-foreground">PKI-based artifact signing</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Timestamping</h4>
                  <p className="text-sm text-muted-foreground">Immutable time records</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Hash Verification</h4>
                  <p className="text-sm text-muted-foreground">Data integrity validation</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <GitBranch className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Merkle Trees</h4>
                  <p className="text-sm text-muted-foreground">Efficient verification</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance Benefits */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Compliance Benefits
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Target className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Auditor Confidence</h3>
                <p className="text-muted-foreground">
                  Provide auditors with cryptographic proof of data integrity and compliance 
                  activities, reducing audit time and increasing confidence in results.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Automated Evidence</h3>
                <p className="text-muted-foreground">
                  Automatically generate and verify compliance evidence with blockchain 
                  timestamps and signatures for seamless audit preparation.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Brain className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Regulatory Trust</h3>
                <p className="text-muted-foreground">
                  Build trust with regulators through transparent, verifiable compliance 
                  processes backed by immutable blockchain records.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Activity className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Continuous Compliance</h3>
                <p className="text-muted-foreground">
                  Enable continuous compliance monitoring with real-time verification 
                  and automated alerts for any deviations from approved configurations.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Ready for Blockchain-Verified Compliance?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Experience the power of cryptographic verification for your compliance artifacts.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Schedule Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Join Waitlist</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
