import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Shield,
  Database,
  Zap,
  CheckCircle,
  Eye,
  Settings,
  Activity,
  Lock,
  Brain,
  Network,
  Layers,
  Globe,
  Server,
  Cloud,
  Cpu,
  Target,
  GitBranch,
  Key,
  Monitor
} from "lucide-react";
import { InteractiveGridPattern } from "@/components/magicui/interactive-grid-pattern";

export default function GRCOSArchitecturePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Interactive Grid Background */}
        <InteractiveGridPattern
          width={60}
          height={60}
          squares={[32, 24]}
          className="absolute inset-0 h-full w-full opacity-50"
          squaresClassName="fill-primary/5 stroke-primary/10 hover:fill-primary/20 transition-all duration-300"
        />
        <div className="container mx-auto px-4 relative z-10">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">GRCOS</span> Architecture
            </h1>
            <h2 className="mt-4 text-2xl font-semibold tracking-tight sm:text-3xl text-muted-foreground">
              Intelligent GRC Orchestration Built for Scale
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl max-w-3xl mx-auto">
              GRCOS transforms traditional GRC management through a revolutionary architecture that combines
              AI-powered orchestration, blockchain-secured verification, and unified environment support.
              Our platform is engineered to deliver enterprise-grade security operations that scale
              intelligently with your organization.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Architectural Foundation */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Architectural Foundation
            </h2>
            <h3 className="mt-4 text-xl font-semibold text-muted-foreground">
              Multi-Domain Intelligence Platform
            </h3>
            <p className="mt-4 text-lg text-muted-foreground">
              GRCOS is built around four core intelligence domains, each powered by specialized AI agents
              and unified through our centralized Dashboard command center:
            </p>
          </div>
        </div>
      </section>

      {/* Core Domains */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="space-y-16">
            {/* LightHouse */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-start space-x-6">
                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-blue-500 text-white text-2xl flex-shrink-0">
                  🏮
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-4">LightHouse - Unified Asset Intelligence</h3>
                  <p className="text-lg text-muted-foreground mb-6">
                    The foundation of GRCOS operations, LightHouse provides comprehensive visibility across your
                    entire digital ecosystem. Our AI agents automatically discover, catalog, and monitor assets
                    across IT, OT, and IoT environments, creating a blockchain-verified single source of truth.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Assets Foundation</h4>
                        <p className="text-sm text-muted-foreground">Immutable asset registry with cryptographic verification</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Orchestrated Visibility</h4>
                        <p className="text-sm text-muted-foreground">Real-time monitoring across all environments</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Integration Backbone</h4>
                        <p className="text-sm text-muted-foreground">Seamless connectivity with existing security tools</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Identity Management</h4>
                        <p className="text-sm text-muted-foreground">Blockchain-enhanced access control and user intelligence</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* ActionCentre */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-start space-x-6">
                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-orange-500 text-white text-2xl flex-shrink-0">
                  ⚡
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-4">ActionCentre - Intelligent Orchestration</h3>
                  <p className="text-lg text-muted-foreground mb-6">
                    Where automation meets intelligence. ActionCentre deploys multi-agent workflows that coordinate
                    complex security operations across your entire infrastructure without human intervention.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Agent Orchestration</h4>
                        <p className="text-sm text-muted-foreground">CrewAI-powered workflows that make intelligent decisions</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Autonomous Response</h4>
                        <p className="text-sm text-muted-foreground">Immediate threat remediation across connected systems</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* ComplianceCentre */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-start space-x-6">
                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-green-500 text-white text-2xl flex-shrink-0">
                  📋
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-4">ComplianceCentre - Unified Control Intelligence</h3>
                  <p className="text-lg text-muted-foreground mb-6">
                    Transforms compliance from a burden into a competitive advantage through intelligent framework
                    orchestration and automated control implementation.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Orchestrated Framework</h4>
                        <p className="text-sm text-muted-foreground">Multi-standard compliance management (NIST, ISO 27001, PCI DSS, HIPAA)</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Intelligent Controls</h4>
                        <p className="text-sm text-muted-foreground">OSCAL-standardized controls that adapt across frameworks</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* TrustCentre */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-start space-x-6">
                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-yellow-500 text-white text-2xl flex-shrink-0">
                  🛡️
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-4">TrustCentre - Verifiable Compliance Proof</h3>
                  <p className="text-lg text-muted-foreground mb-6">
                    Delivers cryptographic proof of compliance without exposing sensitive data, enabling real-time
                    audit readiness and stakeholder confidence.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Blockchain-Secured Evidence</h4>
                        <p className="text-sm text-muted-foreground">Immutable compliance artifacts with cryptographic verification</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">External Trust Verification</h4>
                        <p className="text-sm text-muted-foreground">Auditor-accessible proof without system access</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold">Intelligent Reporting</h4>
                        <p className="text-sm text-muted-foreground">AI-generated compliance communications</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Technology Stack */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Core Technology Stack
            </h2>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Lock className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Blockchain-Secured Foundation</h3>
              <p className="text-muted-foreground text-sm">
                Built on <strong>Hyperledger Fabric</strong>, GRCOS creates an immutable foundation for all security operations.
                Every asset state, configuration change, and compliance artifact receives cryptographic verification.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Brain className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Multi-Agent Orchestration</h3>
              <p className="text-muted-foreground text-sm">
                Powered by <strong>CrewAI</strong>, our AI agents work collaboratively to automate complex security processes
                with intelligent decision-making and essential human oversight.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Shield className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Standardized Control Management</h3>
              <p className="text-muted-foreground text-sm">
                <strong>NIST OSCAL</strong> integration enables unified control implementation across multiple compliance
                frameworks, eliminating complexity of managing separate control sets.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Settings className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Unified Policy Enforcement</h3>
              <p className="text-muted-foreground text-sm">
                <strong>Open Policy Agent (OPA)</strong> provides consistent policy enforcement across IT, OT, and IoT
                environments, ensuring uniform security controls.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Monitor className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Integrated Security Intelligence</h3>
              <p className="text-muted-foreground text-sm">
                <strong>Wazuh SIEM+XDR</strong> integration provides comprehensive security monitoring and threat detection,
                with AI agents automatically correlating events across all environments.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Architectural Advantages */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Architectural Advantages
            </h2>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Globe className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Unified Environment Support</h3>
              <p className="text-muted-foreground">
                Unlike traditional security tools that create silos, GRCOS orchestrates protection across your entire
                digital ecosystem. Our architecture understands the interconnections between operational technology,
                information systems, and IoT devices, providing coordinated security operations.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Zap className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Automation at Scale</h3>
              <p className="text-muted-foreground">
                Multi-agent workflows eliminate manual processes while maintaining intelligent decision-making.
                Security operations scale automatically without proportional increases in staffing requirements.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Key className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Cryptographic Trust</h3>
              <p className="text-muted-foreground">
                Every security operation is cryptographically verified and recorded on our blockchain foundation,
                creating an immutable audit trail that satisfies the most demanding compliance requirements.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Layers className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Framework Agnostic Design</h3>
              <p className="text-muted-foreground">
                GRCOS implements unified controls that satisfy multiple compliance frameworks simultaneously,
                dramatically reducing compliance complexity and operational overhead.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              The GRCOS Difference
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Traditional GRC tools manage compliance. GRCOS orchestrates security. Our architecture transforms
              reactive security management into proactive, intelligent operations that adapt and scale with your
              organization's needs.
            </p>
            <p className="text-lg text-muted-foreground mb-8">
              By combining proven open-source technologies with advanced AI orchestration and blockchain verification,
              GRCOS delivers enterprise-grade security operations accessible to organizations of any size.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Transform Your Security Operations
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
