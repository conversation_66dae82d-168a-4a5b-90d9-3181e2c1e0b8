import { cn } from "@/lib/utils";
import {
  IconAdjustmentsBolt,
  IconCloud,
  IconCurrencyDollar,
  IconEaseInOut,
  IconHeart,
  IconHelp,
  IconRouteAltLeft,
  IconTerminal2,
} from "@tabler/icons-react";

export default function FeaturesSectionDemo() {
  const features = [
    {
      title: "Blockchain-Secured Asset Intelligence",
      description:
        "Blockchain-secured tokenization of IS, OT, and IoT assets with automated discovery, real-time monitoring, and cryptographic verification with immutable audit trails.",
      icon: <IconTerminal2 />,
    },
    {
      title: "Multi-Agent Orchestration",
      description:
        "CrewAI-powered agents collaborate to automate complex security processes with intelligent decision-making and human oversight.",
      icon: <IconEaseInOut />,
    },
    {
      title: "Cryptographic Trust Foundation",
      description:
        "Blockchain-verified compliance evidence with immutable records that provide unshakeable audit trails and stakeholder trust.",
      icon: <IconCurrencyDollar />,
    },
    {
      title: "Unified Control Environment",
      description: "Single security orchestration across IT, OT, and IoT with intelligent coordination between traditionally siloed environments.",
      icon: <IconCloud />,
    },
    {
      title: "Policy-as-Code Enforcement",
      description: "Open Policy Agent integration enables automated policy enforcement and compliance verification across all systems and environments.",
      icon: <IconRouteAltLeft />,
    },
    {
      title: "Intelligent Evidence Collection",
      description:
        "AI agents automatically gather and verify compliance artifacts, maintaining continuous audit readiness with cryptographic proof.",
      icon: <IconHelp />,
    },
  ];
  return (
    <div className="relative z-10 py-20 max-w-7xl mx-auto">
      {/* Header and Sub-header */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl font-display text-neutral-800 dark:text-white mb-4">
          The Operating System for GRC
        </h2>
        <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
          Agentically Automate Security Compliance Across All Environments
        </p>
      </div>
      
      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature, index) => (
          <Feature key={feature.title} {...feature} index={index} />
        ))}
      </div>
    </div>
  );
}

const Feature = ({
  title,
  description,
  icon,
  index
}) => {
  return (
    <div
      className={cn(
        "flex flex-col lg:border-r  py-10 relative group/feature dark:border-neutral-800",
        (index === 0 || index === 3) && "lg:border-l dark:border-neutral-800",
        index < 3 && "lg:border-b dark:border-neutral-800"
      )}>
      {index < 3 && (
        <div
          className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      {index >= 3 && (
        <div
          className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      <div
        className="mb-4 relative z-10 px-10 text-neutral-600 dark:text-neutral-400">
        {icon}
      </div>
      <div className="text-lg font-bold mb-2 relative z-10 px-10">
        <div
          className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover/feature:bg-blue-500 transition-all duration-200 origin-center" />
        <span
          className="group-hover/feature:translate-x-2 transition duration-200 inline-block text-neutral-800 dark:text-neutral-100">
          {title}
        </span>
      </div>
      <p
        className="text-sm text-neutral-600 dark:text-neutral-300 max-w-xs relative z-10 px-10">
        {description}
      </p>
    </div>
  );
};
