import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight } from "lucide-react";
import { IconCloud } from "@/components/ui/icon-cloud";
import { AnimatedGridPattern } from "@/components/magicui/animated-grid-pattern";
import { GlareCard } from "@/components/ui/glare-card";

// Sample integration data
const integrations = [
  {
    id: 1,
    name: "Salesforce",
    category: "CRM",
    description: "Seamlessly sync compliance data with your Salesforce CRM",
    imageUrl: "/favicon.ico",
    status: "Available"
  },
  {
    id: 2,
    name: "Microsoft 365",
    category: "Productivity",
    description: "Integrate with Office 365 for document compliance tracking",
    imageUrl: "/favicon.ico",
    status: "Available"
  },
  {
    id: 3,
    name: "AWS Services",
    category: "Cloud Infrastructure",
    description: "Monitor AWS compliance across your cloud infrastructure",
    imageUrl: "/favicon.ico",
    status: "Available"
  },
  {
    id: 4,
    name: "Slack",
    category: "Communication",
    description: "Get compliance alerts and updates directly in Slack",
    imageUrl: "/favicon.ico",
    status: "Available"
  },
  {
    id: 5,
    name: "<PERSON>ra",
    category: "Project Management",
    description: "Track compliance tasks and issues in Jira workflows",
    imageUrl: "/favicon.ico",
    status: "Coming Soon"
  },
  {
    id: 6,
    name: "ServiceNow",
    category: "ITSM",
    description: "Automate compliance workflows with ServiceNow integration",
    imageUrl: "/favicon.ico",
    status: "Coming Soon"
  },
  {
    id: 7,
    name: "Tableau",
    category: "Analytics",
    description: "Visualize compliance metrics and KPIs in Tableau dashboards",
    imageUrl: "/favicon.ico",
    status: "Available"
  },
  {
    id: 8,
    name: "Docker",
    category: "DevOps",
    description: "Container security and compliance scanning for Docker environments",
    imageUrl: "/favicon.ico",
    status: "Available"
  }
];

export default function IntegrationsPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative min-h-[calc(100vh-4rem)] flex items-center overflow-hidden">
        {/* Animated Grid Background */}
        <AnimatedGridPattern
          numSquares={30}
          maxOpacity={0.1}
          duration={3}
          repeatDelay={1}
          className="absolute inset-0 h-full w-full fill-muted/20 stroke-muted/20"
        />
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
            {/* Left side - Content */}
            <div className="text-center lg:text-left">
              <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
                Platform <span className="text-primary">Integrations</span>
              </h1>
              <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
                Connect Auris GRCOS with your existing tools and systems through our comprehensive
                API and pre-built integrations ecosystem.
              </p>
              <div className="mt-10 flex items-center justify-center lg:justify-start gap-x-6">
                <Button size="lg" asChild>
                  <Link href="/waitlist">
                    Request Integration
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/demo">See All</Link>
                </Button>
              </div>
            </div>
            
            {/* Right side - IconCloud */}
            <div className="flex justify-center lg:justify-start lg:pl-20">
              <div className="scale-150 lg:scale-200">
                <IconCloud />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integrations Grid Section */}
      <section className="py-24 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Available <span className="text-primary">Integrations</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Connect Auris GRCOS with your favorite tools and platforms through our comprehensive
              integration ecosystem. More integrations added regularly.
            </p>
          </div>
          
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-6 gap-y-8 place-items-center">
              {integrations.map((integration) => (
                <GlareCard
                  key={integration.id}
                  className="p-6 flex flex-col justify-between h-80 w-full max-w-sm !aspect-auto"
                >
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Image
                          src={integration.imageUrl}
                          alt={integration.name}
                          width={32}
                          height={32}
                          className="w-8 h-8 object-contain"
                        />
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                        integration.status === 'Available'
                          ? 'bg-green-500/20 text-green-600 dark:text-green-400'
                          : 'bg-yellow-500/20 text-yellow-600 dark:text-yellow-400'
                      }`}>
                        {integration.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold text-foreground">{integration.name}</h3>
                      <p className="text-sm text-muted-foreground font-medium">{integration.category}</p>
                    </div>
                  </div>
                </GlareCard>
              ))}
            </div>
          </div>
          
          <div className="text-center mt-16">
            <p className="text-muted-foreground mb-6">
              Don&apos;t see your integration? We&apos;re always adding new ones.
            </p>
            <Button size="lg" asChild>
              <Link href="/contact">
                Request Integration
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
