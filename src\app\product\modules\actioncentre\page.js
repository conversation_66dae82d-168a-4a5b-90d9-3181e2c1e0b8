import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  CheckSquare,
  Clock,
  Users,
  Zap,
  Settings,
  Shield,
  AlertTriangle,
  Activity,
  FileText,
  Database,
  Lock,
  Eye,
  RefreshCw,
  TrendingUp,
  BarChart3,
  Globe,
  Code,
  Workflow,
  CheckCircle,
  Search,
  Monitor,
  Key,
  Cloud,
  Smartphone,
  Calendar,
  Download,
  UserCheck,
  Building,
  Target,
  Layers,
  GitBranch,
  Play,
  Pause,
  RotateCcw
} from "lucide-react";

export default function ActionCentrePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Action Centre: Where Compliance <span className="text-orange-500">Gets Done</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              From Reactive to Responsive: The Orchestration Engine
            </p>
            <p className="mt-4 text-base leading-7 text-muted-foreground max-w-3xl mx-auto">
              Compliance isn&apos;t about documentation—it&apos;s about action. The GRCOS Action Centre transforms your GRC program from a collection of policies into a living, breathing system that automatically orchestrates remediation, enforces policies, and responds to incidents with precision and speed.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Watch Action Centre Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Start Your Workflow</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Workflow Orchestration: Visual Simplicity, Powerful Results */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Workflow Orchestration: <span className="text-orange-500">Visual Simplicity, Powerful Results</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Design Complex Processes with Drag-and-Drop Ease</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Built on the enterprise-grade Flowable process engine, Action Centre brings visual workflow design to GRC operations, making complex compliance processes as easy to manage as drawing a flowchart.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Visual Process Designer */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Workflow className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Visual Process Designer</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Drag-and-drop interface for building sophisticated workflows</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Pre-built templates for common compliance scenarios</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time process validation and optimization suggestions</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Version control and rollback capabilities for process evolution</span>
                </li>
              </ul>
            </div>

            {/* Intelligent Task Management */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Task Management</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automatic assignment based on skills, availability, and workload</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Dynamic escalation paths that adapt to changing priorities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Progress tracking with predictive completion estimates</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with existing project management and ticketing systems</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Compliance Workflow Library */}
          <div className="rounded-lg border bg-background p-8">
            <h3 className="text-xl font-semibold mb-6">Compliance Workflow Library</h3>
            <p className="text-muted-foreground mb-6">Ready-to-deploy workflows for immediate value:</p>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Control Implementation</h4>
                  <p className="text-sm text-muted-foreground">Step-by-step guidance for security control deployment</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Incident Response</h4>
                  <p className="text-sm text-muted-foreground">Structured incident management from detection to closure</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart3 className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Risk Assessment</h4>
                  <p className="text-sm text-muted-foreground">Automated risk evaluation and treatment workflows</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Search className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Audit Preparation</h4>
                  <p className="text-sm text-muted-foreground">Systematic evidence collection and review processes</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <UserCheck className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Vendor Onboarding</h4>
                  <p className="text-sm text-muted-foreground">Security assessment and approval workflows</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <FileText className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Policy Exception</h4>
                  <p className="text-sm text-muted-foreground">Structured exception request and approval processes</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Policy Enforcement: From Documents to Code */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Policy Enforcement: <span className="text-orange-500">From Documents to Code</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Policies That Enforce Themselves</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Transform static security policies into executable code that automatically enforces compliance across your entire technology stack.
            </p>
          </div>

          {/* Policy-as-Code Engine */}
          <div className="rounded-lg border bg-background p-8 mb-12">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mr-4">
                <Code className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Policy-as-Code Engine</h3>
                <p className="text-sm text-muted-foreground">Powered by Open Policy Agent (OPA) for universal policy enforcement:</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <FileText className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Declarative Policies</h4>
                  <p className="text-sm text-muted-foreground">Express complex requirements in simple, readable language</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Zap className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Real-Time Enforcement</h4>
                  <p className="text-sm text-muted-foreground">Continuous policy validation across all environments</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Globe className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Multi-Environment Support</h4>
                  <p className="text-sm text-muted-foreground">Consistent policy application across cloud, on-premise, and hybrid infrastructure</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <RefreshCw className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Automated Remediation</h4>
                  <p className="text-sm text-muted-foreground">Self-healing systems that correct policy violations automatically</p>
                </div>
              </div>
            </div>
          </div>

          {/* Policy Enforcement Scenarios */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Infrastructure Configuration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Cloud className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Infrastructure Configuration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automatically prevent insecure cloud configurations</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Enforce encryption requirements across all data stores</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Validate access control policies in real-time</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Monitor and correct network security configurations</span>
                </li>
              </ul>
            </div>

            {/* Application Security */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Application Security</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Enforce secure coding practices through automated scanning</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Validate API security configurations before deployment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Monitor application dependencies for known vulnerabilities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automatically update security libraries and configurations</span>
                </li>
              </ul>
            </div>

            {/* Data Protection */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Data Protection</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Enforce data classification and handling requirements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automatically encrypt sensitive data based on policy</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Monitor and prevent unauthorized data access</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Validate data retention and deletion policies</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Remediation Management: Coordinated Response */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Remediation Management: <span className="text-orange-500">Coordinated Response</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Turn Findings Into Fixes</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Action Centre coordinates remediation activities across your entire organization, ensuring nothing falls through the cracks and everything gets resolved efficiently.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Intelligent Prioritization */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Prioritization</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Risk-based remediation prioritization using quantitative analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Business impact assessment for remediation scheduling</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Resource allocation optimization across multiple remediation efforts</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Dependency management to sequence remediation activities effectively</span>
                </li>
              </ul>
            </div>

            {/* Progress Tracking and Verification */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Activity className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Progress Tracking and Verification</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time remediation status across all active efforts</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated verification of remediation effectiveness</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Blockchain-verified evidence of completed remediation actions</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with Change Management systems for controlled updates</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Automated Remediation Playbooks */}
          <div className="rounded-lg border bg-background p-8">
            <h3 className="text-xl font-semibold mb-6">Automated Remediation Playbooks</h3>
            <p className="text-muted-foreground mb-6">Pre-configured response procedures for common security issues:</p>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Vulnerability Management</h4>
                  <p className="text-sm text-muted-foreground">Automated patching workflows with rollback capabilities</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Settings className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Configuration Drift</h4>
                  <p className="text-sm text-muted-foreground">Self-healing infrastructure that returns to secure baselines</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Key className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Access Control Violations</h4>
                  <p className="text-sm text-muted-foreground">Automatic access revocation and privilege adjustment</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckSquare className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Compliance Gaps</h4>
                  <p className="text-sm text-muted-foreground">Structured remediation plans with verification steps</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Security Incidents</h4>
                  <p className="text-sm text-muted-foreground">Coordinated response across multiple teams and systems</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Incident Response Management: Structured Excellence */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Incident Response Management: <span className="text-orange-500">Structured Excellence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">When Seconds Matter, Process Protects</h3>
            <p className="text-lg text-muted-foreground mb-8">
              ActionCentre provides structured incident management that scales from minor security events to major breaches.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Incident Classification and Routing */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Layers className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Incident Classification and Routing</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated incident classification based on threat intelligence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Dynamic escalation based on impact assessment and organizational policies</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Skill-based routing to appropriate response teams</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with threat intelligence feeds for context-aware response</span>
                </li>
              </ul>
            </div>

            {/* Response Orchestration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <GitBranch className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Response Orchestration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Standardized response playbooks for different incident types</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated evidence collection and preservation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Coordinated communication across internal teams and external stakeholders</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time collaboration tools for distributed response teams</span>
                </li>
              </ul>
            </div>

            {/* Recovery and Learning */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Recovery and Learning</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated post-incident review and documentation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Lessons learned integration into future response procedures</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous improvement of response playbooks based on outcomes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with threat intelligence to improve future detection</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Smart Contracts: Blockchain-Enforced Policies */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Smart Contracts: <span className="text-orange-500">Blockchain-Enforced Policies</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Unstoppable Policy Enforcement</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Leverage blockchain smart contracts to create policies that cannot be circumvented, providing the highest level of assurance for critical security controls.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Immutable Policy Execution */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Immutable Policy Execution</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Smart contracts that execute security policies automatically</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Cryptographic verification of policy compliance</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Tamper-proof audit trail of all policy enforcement actions</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Decentralized enforcement that doesn&apos;t depend on single points of failure</span>
                </li>
              </ul>
            </div>

            {/* Use Cases for Smart Contract Enforcement */}
            <div className="rounded-lg border bg-background p-8">
              <h3 className="text-xl font-semibold mb-6">Use Cases for Smart Contract Enforcement</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-sm mb-2 flex items-center">
                    <Key className="h-4 w-4 text-orange-500 mr-2" />
                    Access Control
                  </h4>
                  <ul className="space-y-1 text-sm text-muted-foreground ml-6">
                    <li>• Multi-signature approval requirements for sensitive operations</li>
                    <li>• Time-based access controls that automatically expire</li>
                    <li>• Cryptographic proof of authorization for audit purposes</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2 flex items-center">
                    <Settings className="h-4 w-4 text-orange-500 mr-2" />
                    Change Management
                  </h4>
                  <ul className="space-y-1 text-sm text-muted-foreground ml-6">
                    <li>• Immutable approval workflows for critical system changes</li>
                    <li>• Automated rollback capabilities for failed changes</li>
                    <li>• Cryptographic verification of change authorization</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2 flex items-center">
                    <CheckSquare className="h-4 w-4 text-orange-500 mr-2" />
                    Compliance Verification
                  </h4>
                  <ul className="space-y-1 text-sm text-muted-foreground ml-6">
                    <li>• Automated compliance checking with cryptographic proof</li>
                    <li>• Immutable records of compliance status over time</li>
                    <li>• Smart contract-enforced remediation requirements</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Excellence: Connected Operations */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Integration Excellence: <span className="text-orange-500">Connected Operations</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Seamless Workflow Across Your Security Stack</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Action Centre doesn&apos;t operate in isolation—it orchestrates activities across your entire security ecosystem.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* GRCOS Module Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Database className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">GRCOS Module Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>LightHouse:</strong> Automated response to security monitoring alerts</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>ComplianceCentre:</strong> Policy enforcement based on framework requirements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Trust Centre:</strong> Evidence collection during remediation activities</span>
                </li>
              </ul>
            </div>

            {/* External System Integration */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Globe className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">External System Integration</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>SIEM/SOAR Platforms:</strong> Automated incident response based on security events</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Ticketing Systems:</strong> Seamless integration with existing IT service management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Change Management:</strong> Controlled implementation of security improvements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Identity Management:</strong> Automated access control remediation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Cloud Platforms:</strong> Policy enforcement across multi-cloud environments</span>
                </li>
              </ul>
            </div>
          </div>

          {/* API-First Architecture */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mr-4">
                <Code className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">API-First Architecture</h3>
                <p className="text-sm text-muted-foreground">Built for seamless integration with any system</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Globe className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">RESTful APIs</h4>
                  <p className="text-sm text-muted-foreground">Integration with any system</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Zap className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Webhook Support</h4>
                  <p className="text-sm text-muted-foreground">Real-time event processing</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Standard Protocols</h4>
                  <p className="text-sm text-muted-foreground">Enterprise compatibility</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Settings className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Custom Connectors</h4>
                  <p className="text-sm text-muted-foreground">Unique requirements</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI-Powered Orchestration */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              AI-Powered <span className="text-orange-500">Orchestration</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Intelligence That Accelerates Action</h3>
            <p className="text-lg text-muted-foreground mb-8">
              CrewAI integration provides intelligent assistance that makes complex remediation decisions simple and effective.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Smart Remediation Guidance */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Smart Remediation Guidance</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>AI-powered analysis of remediation options with cost-benefit analysis</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated prioritization based on risk, impact, and resource availability</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Predictive analysis of remediation effectiveness</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Learning from past remediation outcomes to improve future recommendations</span>
                </li>
              </ul>
            </div>

            {/* Natural Language Workflow Creation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Natural Language Workflow Creation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Describe complex workflows in plain English</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>AI-powered translation of business requirements into executable processes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automatic optimization of workflow efficiency</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Intelligent suggestion of workflow improvements based on execution patterns</span>
                </li>
              </ul>
            </div>

            {/* Intelligent Escalation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Escalation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>AI-powered assessment of when to escalate issues</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Dynamic escalation paths based on context and urgency</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Predictive analysis of resource requirements for complex incidents</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated stakeholder communication based on situation severity</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Control Implementation: Guided Excellence */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Control Implementation: <span className="text-orange-500">Guided Excellence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">From Requirements to Reality</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Action Centre provides step-by-step guidance for implementing security controls, ensuring consistent deployment across your organization.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Implementation Workflows */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Workflow className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Implementation Workflows</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Framework-specific control implementation guidance</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated verification of control effectiveness</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with technical implementation tools</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Documentation generation for audit purposes</span>
                </li>
              </ul>
            </div>

            {/* Control Testing and Validation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <CheckSquare className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Control Testing and Validation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated testing of control effectiveness</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous monitoring of control operation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Evidence collection for audit purposes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Performance metrics and improvement recommendations</span>
                </li>
              </ul>
            </div>

            {/* Cross-Framework Mapping */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Globe className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Cross-Framework Mapping</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Implement controls once, satisfy multiple frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated mapping of control implementations to framework requirements</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Gap analysis across different compliance standards</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Optimization recommendations for multi-framework compliance</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* The Action Advantage */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              The <span className="text-orange-500">Action Advantage</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Why Orchestration Matters</h3>
            <p className="text-lg text-muted-foreground mb-8">
              In complex technology environments, manual coordination of security activities creates delays, errors, and gaps. Action Centre&apos;s orchestration approach provides:
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Speed */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Speed</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated response to security events reduces time-to-remediation by 80%</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Parallel processing of multiple remediation activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Elimination of manual coordination overhead</span>
                </li>
              </ul>
            </div>

            {/* Accuracy */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Accuracy</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Standardized processes eliminate human error</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated verification ensures complete remediation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Consistent application of policies across all environments</span>
                </li>
              </ul>
            </div>

            {/* Compliance */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Compliance</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Built-in compliance verification for all activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated documentation for audit purposes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous monitoring of control effectiveness</span>
                </li>
              </ul>
            </div>

            {/* Scalability */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Scalability</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Orchestration scales with your business growth</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Automated resource allocation across multiple priorities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span>Consistent process execution regardless of team size</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Measurable Results */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Measurable <span className="text-orange-500">Results</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Outcomes You Can Track</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Action Centre provides clear metrics that demonstrate the value of orchestrated GRC operations:
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Efficiency Metrics */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Efficiency Metrics</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>70%</strong> reduction in mean time to remediation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>90%</strong> automation of routine compliance tasks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>60%</strong> improvement in audit preparation efficiency</span>
                </li>
              </ul>
            </div>

            {/* Quality Metrics */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <CheckSquare className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Quality Metrics</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>95%</strong> first-time fix rate for security issues</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>85%</strong> reduction in compliance finding recurrence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>99%</strong> accuracy in control implementation verification</span>
                </li>
              </ul>
            </div>

            {/* Business Impact */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 mb-6">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Business Impact</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>50%</strong> reduction in compliance program costs</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>40%</strong> improvement in audit outcomes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span><strong>25%</strong> reduction in cyber insurance premiums</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Ready to Transform Compliance Into Action? */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Ready to Transform Compliance Into <span className="text-orange-500">Action?</span>
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              See how Action Centre turns your GRC program from reactive documentation into proactive security orchestration.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Watch Action Centre Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Start Your Workflow</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
