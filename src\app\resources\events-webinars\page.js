import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Calendar, Clock, Users, MapPin, Video, Play, Download } from "lucide-react";

export default function EventsWebinarsPage() {
  const upcomingEvents = [
    {
      id: "iso-27001-masterclass",
      title: "ISO 27001 Implementation Masterclass",
      type: "Webinar",
      date: "2024-02-15",
      time: "2:00 PM EST",
      duration: "60 minutes",
      format: "Online",
      description: "Join our compliance experts for a deep dive into ISO 27001 implementation best practices, common pitfalls, and how to achieve certification faster.",
      speakers: [
        { name: "<PERSON>", role: "Chief Compliance Officer", company: "<PERSON>ris" },
        { name: "Dr. <PERSON>", role: "ISO 27001 Lead Auditor", company: "CertifyPro" }
      ],
      agenda: [
        "ISO 27001 framework overview",
        "Gap analysis and risk assessment",
        "Control implementation strategies",
        "Certification timeline and process",
        "Q&A session"
      ],
      registrationLink: "/events/register/iso-27001-masterclass",
      featured: true
    },
    {
      id: "soc2-automation-workshop",
      title: "SOC 2 Automation Workshop",
      type: "Workshop",
      date: "2024-02-22",
      time: "1:00 PM EST",
      duration: "90 minutes",
      format: "Online",
      description: "Hands-on workshop demonstrating how to automate SOC 2 compliance processes and reduce audit preparation time by 80%.",
      speakers: [
        { name: "Jennifer Rodriguez", role: "Product Manager", company: "Auris" },
        { name: "Robert Kim", role: "SOC 2 Specialist", company: "Auris" }
      ],
      agenda: [
        "SOC 2 automation overview",
        "Live platform demonstration",
        "Evidence collection automation",
        "Continuous monitoring setup",
        "Interactive Q&A"
      ],
      registrationLink: "/events/register/soc2-automation-workshop",
      featured: true
    },
    {
      id: "compliance-trends-2024",
      title: "Compliance Trends 2024: What's Next?",
      type: "Panel Discussion",
      date: "2024-03-05",
      time: "3:00 PM EST",
      duration: "45 minutes",
      format: "Online",
      description: "Industry experts discuss emerging compliance trends, regulatory changes, and what organizations need to prepare for in 2024.",
      speakers: [
        { name: "Lisa Wang", role: "Compliance Director", company: "TechCorp" },
        { name: "David Miller", role: "Risk Management Expert", company: "RiskAdvisors" },
        { name: "Maria Santos", role: "Chief Risk Officer", company: "Auris" }
      ],
      agenda: [
        "2024 regulatory landscape",
        "Emerging compliance challenges",
        "Technology trends in compliance",
        "Predictions and recommendations",
        "Audience Q&A"
      ],
      registrationLink: "/events/register/compliance-trends-2024",
      featured: false
    }
  ];

  const pastEvents = [
    {
      id: "multi-framework-strategy",
      title: "Multi-Framework Compliance Strategy",
      type: "Webinar",
      date: "2024-01-18",
      duration: "60 minutes",
      attendees: 450,
      description: "Learn how to implement unified controls that satisfy multiple compliance frameworks simultaneously.",
      recordingAvailable: true,
      slidesAvailable: true,
      featured: true
    },
    {
      id: "hipaa-healthcare-tech",
      title: "HIPAA Compliance for Healthcare Technology",
      type: "Workshop",
      date: "2024-01-10",
      duration: "90 minutes",
      attendees: 320,
      description: "Comprehensive workshop on HIPAA compliance requirements for healthcare technology companies.",
      recordingAvailable: true,
      slidesAvailable: true,
      featured: true
    },
    {
      id: "pci-dss-payment-security",
      title: "PCI DSS Payment Security Essentials",
      type: "Webinar",
      date: "2023-12-14",
      duration: "45 minutes",
      attendees: 280,
      description: "Essential guide to PCI DSS compliance for organizations handling payment card data.",
      recordingAvailable: true,
      slidesAvailable: false,
      featured: false
    },
    {
      id: "gdpr-data-protection",
      title: "GDPR Data Protection Best Practices",
      type: "Panel Discussion",
      date: "2023-12-07",
      duration: "60 minutes",
      attendees: 380,
      description: "Expert panel discussing GDPR implementation challenges and best practices.",
      recordingAvailable: true,
      slidesAvailable: true,
      featured: false
    }
  ];

  const eventTypes = [
    { name: "Webinars", description: "Educational sessions on compliance topics", icon: Video },
    { name: "Workshops", description: "Hands-on training and demonstrations", icon: Users },
    { name: "Panel Discussions", description: "Expert insights and industry trends", icon: Calendar }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Events & <span className="text-primary">Webinars</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Join our compliance experts for educational webinars, hands-on workshops, 
              and industry discussions. Learn best practices and stay ahead of compliance trends.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="#upcoming-events">
                  View Upcoming Events
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="#past-events">Browse Recordings</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Event Types */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-12">
              <h2 className="text-2xl font-bold tracking-tight sm:text-3xl mb-4">
                Types of Events
              </h2>
              <p className="text-lg text-muted-foreground">
                Different formats to suit your learning preferences and schedule
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              {eventTypes.map((type, index) => {
                const IconComponent = type.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-primary-foreground" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{type.name}</h3>
                    <p className="text-muted-foreground">{type.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Upcoming Events */}
      <section id="upcoming-events" className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Upcoming Events
              </h2>
              <p className="text-lg text-muted-foreground">
                Register for our upcoming webinars and workshops
              </p>
            </div>

            <div className="space-y-8">
              {upcomingEvents.map((event) => (
                <div key={event.id} className={`rounded-lg border p-8 ${
                  event.featured ? 'bg-primary/5 border-primary/20' : 'bg-background'
                }`}>
                  <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    <div className="lg:col-span-2">
                      <div className="flex items-center space-x-4 mb-4">
                        <span className={`inline-block px-3 py-1 text-sm font-medium rounded-full ${
                          event.featured 
                            ? 'bg-primary text-primary-foreground' 
                            : 'bg-muted text-muted-foreground'
                        }`}>
                          {event.type}
                        </span>
                        {event.featured && (
                          <span className="inline-block px-3 py-1 text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                            Featured
                          </span>
                        )}
                      </div>
                      
                      <h3 className="text-2xl font-bold mb-3">{event.title}</h3>
                      <p className="text-muted-foreground mb-6">{event.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        <div className="flex items-center space-x-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{new Date(event.date).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{event.time} ({event.duration})</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{event.format}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>{event.speakers.length} Speaker{event.speakers.length > 1 ? 's' : ''}</span>
                        </div>
                      </div>

                      <div className="mb-6">
                        <h4 className="font-semibold mb-3">Speakers:</h4>
                        <div className="space-y-2">
                          {event.speakers.map((speaker, index) => (
                            <div key={index} className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                                <Users className="w-4 h-4" />
                              </div>
                              <div>
                                <div className="font-medium">{speaker.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {speaker.role}, {speaker.company}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="lg:col-span-1">
                      <div className="rounded-lg border bg-background p-6">
                        <h4 className="font-semibold mb-4">Agenda</h4>
                        <ul className="space-y-2 mb-6">
                          {event.agenda.map((item, index) => (
                            <li key={index} className="text-sm text-muted-foreground">
                              • {item}
                            </li>
                          ))}
                        </ul>
                        
                        <Button className="w-full" asChild>
                          <Link href={event.registrationLink}>
                            Register Now
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Past Events */}
      <section id="past-events" className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Past Events & Recordings
              </h2>
              <p className="text-lg text-muted-foreground">
                Catch up on previous sessions with recordings and presentation materials
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {pastEvents.map((event) => (
                <div key={event.id} className={`rounded-lg border p-6 ${
                  event.featured ? 'bg-primary/5 border-primary/20' : 'bg-background'
                }`}>
                  <div className="flex items-center space-x-4 mb-4">
                    <span className={`inline-block px-3 py-1 text-sm font-medium rounded-full ${
                      event.featured 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      {event.type}
                    </span>
                    {event.featured && (
                      <span className="inline-block px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                        Popular
                      </span>
                    )}
                  </div>
                  
                  <h3 className="text-xl font-bold mb-3">{event.title}</h3>
                  <p className="text-muted-foreground mb-4">{event.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-6">
                    <span>{new Date(event.date).toLocaleDateString()}</span>
                    <span>{event.duration}</span>
                    <span>{event.attendees} attendees</span>
                  </div>

                  <div className="flex items-center space-x-3">
                    {event.recordingAvailable && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/events/recording/${event.id}`}>
                          <Play className="mr-2 h-4 w-4" />
                          Watch Recording
                        </Link>
                      </Button>
                    )}
                    {event.slidesAvailable && (
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/events/slides/${event.id}`}>
                          <Download className="mr-2 h-4 w-4" />
                          Download Slides
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 text-primary-foreground">
              Never Miss an Event
            </h2>
            <p className="text-lg text-primary-foreground/80 mb-8">
              Subscribe to our newsletter to get notified about upcoming webinars, workshops, and compliance insights.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/newsletter">
                  Subscribe to Newsletter
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
