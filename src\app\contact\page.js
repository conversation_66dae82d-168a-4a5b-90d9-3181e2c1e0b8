import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Mail, Phone, MapPin, Clock, MessageSquare, Users } from "lucide-react";

export default function ContactPage() {
  const contactMethods = [
    {
      title: "Sales Inquiries",
      description: "Ready to transform your compliance management?",
      icon: Users,
      action: "Book Demo",
      href: "/demo/request",
      details: "Schedule a personalized demonstration"
    },
    {
      title: "Customer Support",
      description: "Need help with your existing account?",
      icon: MessageSquare,
      action: "Get Support",
      href: "/contact/support",
      details: "24/7 support for all customers"
    },
    {
      title: "Partnership Opportunities",
      description: "Interested in partnering with us?",
      icon: Users,
      action: "Contact Partnerships",
      href: "/contact/partnerships",
      details: "Technology and business partnerships"
    }
  ];

  const officeInfo = [
    {
      title: "Global Headquarters",
      address: "123 Compliance Street\nSan Francisco, CA 94105\nUnited States",
      phone: "+****************",
      email: "<EMAIL>"
    },
    {
      title: "European Office",
      address: "456 Regulation Avenue\nLondon, EC1A 1BB\nUnited Kingdom",
      phone: "+44 20 7123 4567",
      email: "<EMAIL>"
    }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Get in <span className="text-primary">Touch</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Whether you&apos;re ready to streamline your compliance, need support, or want to explore partnership opportunities,
              our team is here to help.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              How Can We Help?
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Choose the best way to reach us based on your needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {contactMethods.map((method) => {
              const Icon = method.icon;
              return (
                <div key={method.title} className="rounded-lg border bg-background p-8 text-center">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                    <Icon className="h-8 w-8 text-primary-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{method.title}</h3>
                  <p className="text-muted-foreground mb-4">{method.description}</p>
                  <p className="text-sm text-muted-foreground mb-6">{method.details}</p>
                  <Button className="w-full" asChild>
                    <Link href={method.href}>
                      {method.action}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Office Information */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Our Offices
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              We&apos;re a global team with offices around the world.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {officeInfo.map((office) => (
              <div key={office.title} className="rounded-lg border p-8">
                <h3 className="text-xl font-semibold mb-6">{office.title}</h3>
                
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <MapPin className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <div className="font-medium mb-1">Address</div>
                      <div className="text-muted-foreground whitespace-pre-line">{office.address}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Phone className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <div className="font-medium mb-1">Phone</div>
                      <div className="text-muted-foreground">{office.phone}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Mail className="h-5 w-5 text-primary mt-1" />
                    <div>
                      <div className="font-medium mb-1">Email</div>
                      <div className="text-muted-foreground">{office.email}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Support Hours */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-8">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                  <Clock className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Support Hours</h3>
                <div className="space-y-2 text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Monday - Friday</span>
                    <span>9:00 AM - 6:00 PM PST</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Saturday</span>
                    <span>10:00 AM - 4:00 PM PST</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sunday</span>
                    <span>Closed</span>
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between font-medium text-foreground">
                      <span>Emergency Support</span>
                      <span>24/7 Available</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                  <MessageSquare className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Response Times</h3>
                <div className="space-y-3">
                  <div>
                    <div className="font-medium mb-1">Sales Inquiries</div>
                    <div className="text-muted-foreground">Within 2 hours during business hours</div>
                  </div>
                  <div>
                    <div className="font-medium mb-1">Customer Support</div>
                    <div className="text-muted-foreground">Within 4 hours for standard issues</div>
                  </div>
                  <div>
                    <div className="font-medium mb-1">Emergency Support</div>
                    <div className="text-muted-foreground">Within 30 minutes, 24/7</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Link */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Have Questions?
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Check out our frequently asked questions or reach out to our team directly.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/resources/faq">
                  View FAQ
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="mailto:<EMAIL>">Send Email</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Ready to Get Started?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Join hundreds of organizations that trust Auris Compliance to manage their regulatory requirements.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo/request">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/solutions">View Solutions</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
