import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

// Navigation data structure for mega menus
export const navigationData = {
  product: {
    title: "Product",
    sections: [
      {
        title: "Platform",
        items: [
          { name: "GRCOS Architecture", href: "/product/grcos-architecture", description: "Core platform architecture and infrastructure" },
          { name: "Integrations", href: "/product/integrations", description: "Third-party integrations and APIs" },
          { name: "Platform Compliance", href: "/product/platform-compliance", description: "Built-in compliance features and controls" }
        ]
      },
      {
        title: "Capabilities",
        items: [
          { name: "Unified Control Environment", href: "/product/capabilities/unified-control-environment", description: "Coordinate IT/OT/IoT environments" },
          { name: "Blockchain-Enhanced CMDB", href: "/product/capabilities/blockchain-enhanced-cmdb", description: "Cryptographic compliance verification" },
          { name: "Multi-Agent Orchestration", href: "/product/capabilities/multi-agent-orchestration", description: "Intelligent GRC automation" },
          { name: "All Features", href: "/product/capabilities/all-features", description: "Comprehensive platform features" }
        ]
      },
      {
        title: "Modules",
        items: [
          { name: "LightHouse", href: "/product/modules/lighthouse", description: "Asset discovery and inventory management", color: "blue" },
          { name: "ActionCentre", href: "/product/modules/actioncentre", description: "Incident response and remediation", color: "orange" },
          { name: "ComplianceCentre", href: "/product/modules/compliancecentre", description: "Framework management and reporting", color: "green" },
          { name: "TrustCentre", href: "/product/modules/trustcentre", description: "Evidence management and verification", color: "yellow" }
        ]
      }
    ]
  },
  solutions: {
    title: "Solutions",
    sections: [
      {
        title: "By Framework",
        items: [
          { name: "NIST 800-82", href: "/solutions/frameworks#nist-800-82", description: "Industrial control systems security" },
          { name: "ISO 27001", href: "/solutions/frameworks#iso-27001", description: "Information security management" },
          { name: "HIPAA", href: "/solutions/frameworks#hipaa", description: "Healthcare data protection" },
          { name: "SOC 2", href: "/solutions/frameworks#soc-2", description: "Service organization controls" },
          { name: "All Frameworks", href: "/solutions/frameworks", description: "Complete framework coverage" }
        ]
      },
      {
        title: "By Industry",
        items: [
          { name: "Financial Services", href: "/solutions/industry/financial-services", description: "Banking and fintech compliance" },
          { name: "Healthcare", href: "/solutions/industry/healthcare", description: "Medical device and patient data security" },
          { name: "Technology", href: "/solutions/industry/technology", description: "Software and SaaS compliance" },
          { name: "Manufacturing & Utilities", href: "/solutions/industry/manufacturing-utilities", description: "Industrial and critical infrastructure" }
        ]
      },
      {
        title: "By Company Size",
        items: [
          { name: "Startups", href: "/solutions/size/startups", description: "Build compliance from day one" },
          { name: "SME", href: "/solutions/size/smes", description: "Scale your compliance program" },
          { name: "Enterprise", href: "/solutions/size/enterprise", description: "Complex multi-framework management" }
        ]
      }
    ]
  },
  resources: {
    title: "Resources",
    sections: [
      {
        title: "Learn",
        items: [
          { name: "Blog", href: "/resources/blog", description: "Latest insights and industry trends" },
          { name: "Guides", href: "/resources/guides", description: "Implementation and best practices" },
          { name: "Case Studies", href: "/resources/case-studies", description: "Customer success stories" },
          { name: "FAQ", href: "/resources/faq", description: "Frequently asked questions" }
        ]
      },
      {
        title: "Documentation",
        items: [
          { name: "API Reference", href: "/resources/docs/api", description: "Developer documentation" },
          { name: "Platform Docs", href: "/resources/docs", description: "User guides and tutorials" }
        ]
      },
      {
        title: "Events",
        items: [
          { name: "Webinars", href: "/resources/events-webinars", description: "Live and recorded sessions" }
        ]
      }
    ]
  },
  company: {
    title: "Company",
    sections: [
      {
        title: "About",
        items: [
          { name: "Our Mission", href: "/company/mission", description: "Democratizing enterprise-grade GRC" },
          { name: "Team", href: "/company/team", description: "Meet the people behind GRCOS" },
          { name: "Careers", href: "/company/careers", description: "Join our growing team" },
          { name: "Partners", href: "/company/partners", description: "Technology and channel partners" }
        ]
      }
    ]
  }
};

// Footer data structure
export const footerData = {
  sections: [
    {
      title: "Product",
      links: [
        { name: "GRCOS Architecture", href: "/product/grcos-architecture" },
        { name: "Integrations", href: "/product/integrations" },
        { name: "Platform Compliance", href: "/product/platform-compliance" },
        { name: "All Features", href: "/product/capabilities/all-features" }
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "Frameworks", href: "/solutions/frameworks" },
        { name: "Financial Services", href: "/solutions/industry/financial-services" },
        { name: "Healthcare", href: "/solutions/industry/healthcare" },
        { name: "Enterprise", href: "/solutions/size/enterprise" }
      ]
    },
    {
      title: "Resources",
      links: [
        { name: "Blog", href: "/resources/blog" },
        { name: "Guides", href: "/resources/guides" },
        { name: "Case Studies", href: "/resources/case-studies" },
        { name: "FAQ", href: "/resources/faq" }
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About", href: "/company" },
        { name: "Mission", href: "/company/mission" },
        { name: "Team", href: "/company/team" },
        { name: "Careers", href: "/company/careers" }
      ]
    },
    {
      title: "Legal",
      links: [
        { name: "Privacy Policy", href: "/legal/privacy-policy" },
        { name: "Terms of Service", href: "/legal/terms-of-service" },
        { name: "Cookie Policy", href: "/legal/cookie-policy" },
        { name: "Security", href: "/legal/security" }
      ]
    }
  ]
};
