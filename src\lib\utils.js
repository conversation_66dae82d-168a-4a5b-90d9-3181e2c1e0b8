import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

// Navigation data structure for mega menus
export const navigationData = {
  product: {
    title: "Product",
    sections: [
      {
        title: "Platform",
        items: [
          { name: "GRCOS Architecture", href: "/product/grcos-architecture", description: "Core platform architecture and infrastructure" },
          { name: "Integrations", href: "/product/integrations", description: "Third-party integrations and APIs" },
          { name: "Platform Compliance", href: "/product/platform-compliance", description: "Built-in compliance features and controls" }
        ]
      },
      {
        title: "Capabilities",
        items: [
          { name: "Unified Control Environment", href: "/product/capabilities", description: "IS/OT/IoT coordination" },
          { name: "Blockchain-Enhanced CMDB", href: "/product/capabilities", description: "Cryptographic compliance verification" },
          { name: "Multi-Agent Orchestration", href: "/product/capabilities", description: "Intelligent GRC automation" },
          { name: "All Features", href: "/product/capabilities", description: "Comprehensive platform features list" }
        ]
      },
      {
        title: "Modules",
        items: [
          { name: "LightHouse", href: "/product/modules/lighthouse", description: "Integration hub for third-party tools" },
          { name: "ComplianceCentre", href: "/product/modules/compliancecentre", description: "Compliance management and monitoring" },
          { name: "ActionCentre", href: "/product/modules/actioncentre", description: "Automated remediation and workflows" },
          { name: "TrustCentre", href: "/product/modules/trustcentre", description: "Risk assessment and trust management" }
        ]
      }
    ]
  },
  solutions: {
    title: "Solutions",
    sections: [
      {
        title: "By Framework",
        items: [
          { name: "NIST 800-82", href: "/solutions/frameworks", description: "Industrial control systems security guidance" },
          { name: "ISO 27001", href: "/solutions/frameworks", description: "Information security management compliance" },
          { name: "HIPAA", href: "/solutions/frameworks", description: "Healthcare data protection compliance" },
          { name: "All Frameworks", href: "/solutions/frameworks", description: "View all supported compliance frameworks" }
        ]
      },
      {
        title: "By Industry",
        items: [
          { name: "Manufacturing", href: "/solutions/industry/manufacturing", description: "Industrial compliance and security solutions" },
          { name: "Utilities", href: "/solutions/industry/utilities", description: "Critical infrastructure protection" },
          { name: "Financial Services", href: "/solutions/industry/financial-services", description: "Financial compliance and risk management" },
          { name: "Healthcare", href: "/solutions/industry/healthcare", description: "Healthcare compliance and data protection" }
        ]
      },
      {
        title: "By Size",
        items: [
          { name: "SME", href: "/solutions/size/sme", description: "Small & Medium Enterprises" },
          { name: "Enterprise", href: "/solutions/size/enterprise", description: "Large enterprise organizations" }
        ]
      }
    ]
  },
  company: {
    title: "Company",
    sections: [
      {
        title: "About",
        items: [
          { name: "Mission", href: "/company/mission", description: "Our mission and vision for compliance" },
          { name: "Team", href: "/company/team", description: "Meet our leadership and team members" },
          { name: "Partners", href: "/company/partners", description: "Technology and business partnerships" },
          { name: "Careers", href: "/company/careers", description: "Join our growing team" }
        ]
      }
    ]
  },
  resources: {
    title: "Resources",
    sections: [
      {
        title: "Learn",
        items: [
          { name: "Documentation", href: "/resources/docs", description: "Technical documentation and guides" },
          { name: "Guides", href: "/resources/guides", description: "Best practices and implementation guides" },
          { name: "FAQ", href: "/resources/faq", description: "Frequently asked questions" },
          { name: "Blog", href: "/resources/blog", description: "Latest insights and updates" }
        ]
      },
      {
        title: "Support",
        items: [
          { name: "Case Studies", href: "/resources/case-studies", description: "Customer success stories" },
          { name: "Events & Webinars", href: "/resources/events-webinars", description: "Upcoming events and webinars" }
        ]
      }
    ]
  }
};

// Footer navigation data
export const footerData = {
  sections: [
    {
      title: "Legal",
      items: [
        { name: "Privacy Policy", href: "/legal/privacy-policy" },
        { name: "Terms of Service", href: "/legal/terms-of-service" },
        { name: "Cookie Policy", href: "/legal/cookie-policy" },
        { name: "Security", href: "/legal/security" }
      ]
    },
    {
      title: "Company",
      items: [
        { name: "Careers", href: "/company/careers" },
        { name: "Partners", href: "/company/partners" },
        { name: "Contact", href: "/contact" }
      ]
    },
    {
      title: "Resources",
      items: [
        { name: "Documentation", href: "/resources/docs" },
        { name: "API", href: "/resources/docs/api" },
        { name: "Support", href: "/contact/support" }
      ]
    }
  ]
};
