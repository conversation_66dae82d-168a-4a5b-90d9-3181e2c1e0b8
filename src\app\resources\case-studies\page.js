import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Building, Users, Clock, CheckCircle, TrendingUp, Shield } from "lucide-react";

export default function CaseStudiesPage() {
  const caseStudies = [
    {
      id: "techcorp-soc2",
      company: "TechCorp",
      industry: "SaaS Technology",
      framework: "SOC 2 Type II",
      timeline: "6 months",
      employees: "500+",
      challenge: "Needed SOC 2 compliance for enterprise customers",
      solution: "Implemented unified control framework with automated evidence collection",
      results: [
        "Achieved SOC 2 Type II certification in 6 months",
        "Reduced audit preparation time by 75%",
        "Increased enterprise sales by 40%",
        "Automated 90% of compliance evidence collection"
      ],
      quote: "Auris GRCOS transformed our compliance process. What used to take months of manual work now happens automatically.",
      author: "<PERSON>",
      role: "Chief Security Officer",
      featured: true
    },
    {
      id: "healthtech-hipaa",
      company: "HealthTech Solutions",
      industry: "Healthcare Technology",
      framework: "HIPAA",
      timeline: "4 months",
      employees: "200+",
      challenge: "Complex HIPAA compliance for healthcare data processing",
      solution: "Deployed comprehensive HIPAA controls with real-time monitoring",
      results: [
        "100% HIPAA compliance across all systems",
        "Reduced compliance overhead by 60%",
        "Automated breach risk assessments",
        "Streamlined BAA management"
      ],
      quote: "The platform's HIPAA automation gave us confidence to scale our healthcare solutions.",
      author: "Dr. Michael Chen",
      role: "Chief Technology Officer",
      featured: true
    },
    {
      id: "fintech-multi-framework",
      company: "FinanceFlow",
      industry: "Financial Services",
      framework: "PCI DSS + SOC 2",
      timeline: "8 months",
      employees: "300+",
      challenge: "Multiple compliance requirements for payment processing",
      solution: "Unified control implementation satisfying multiple frameworks",
      results: [
        "Achieved PCI DSS Level 1 certification",
        "Maintained SOC 2 Type II compliance",
        "Single control set for multiple frameworks",
        "Reduced compliance costs by 50%"
      ],
      quote: "One platform, multiple frameworks - exactly what we needed for our complex compliance requirements.",
      author: "Jennifer Rodriguez",
      role: "Chief Risk Officer",
      featured: true
    },
    {
      id: "manufacturing-nist",
      company: "Industrial Systems Inc",
      industry: "Manufacturing",
      framework: "NIST 800-82",
      timeline: "10 months",
      employees: "1000+",
      challenge: "Securing industrial control systems and OT networks",
      solution: "Specialized ICS security controls with network segmentation",
      results: [
        "Comprehensive OT security implementation",
        "Network segmentation across 15 facilities",
        "Real-time ICS monitoring",
        "Zero security incidents post-implementation"
      ],
      quote: "Finally, a compliance solution that understands industrial environments.",
      author: "Robert Kim",
      role: "Chief Information Security Officer",
      featured: false
    }
  ];

  const metrics = [
    { label: "Average Implementation Time", value: "6 months", icon: Clock },
    { label: "Compliance Success Rate", value: "100%", icon: CheckCircle },
    { label: "Average Cost Reduction", value: "60%", icon: TrendingUp },
    { label: "Customer Satisfaction", value: "98%", icon: Users }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Customer <span className="text-primary">Success Stories</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              See how organizations across industries have transformed their compliance processes 
              with Auris GRCOS. Real results from real customers.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Discuss Your Needs</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Metrics */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
              {metrics.map((metric, index) => {
                const IconComponent = metric.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-primary-foreground" />
                    </div>
                    <div className="text-3xl font-bold text-primary mb-2">{metric.value}</div>
                    <div className="text-sm text-muted-foreground">{metric.label}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Case Studies */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Featured Success Stories
              </h2>
              <p className="text-lg text-muted-foreground">
                In-depth case studies showcasing transformational compliance implementations
              </p>
            </div>

            <div className="space-y-16">
              {caseStudies.filter(study => study.featured).map((study, index) => (
                <div key={study.id} className={`grid grid-cols-1 gap-12 lg:grid-cols-2 items-center ${
                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                }`}>
                  <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                        <Building className="h-6 w-6 text-primary-foreground" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold">{study.company}</h3>
                        <p className="text-muted-foreground">{study.industry}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="font-semibold text-primary">{study.framework}</div>
                        <div className="text-sm text-muted-foreground">Framework</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="font-semibold text-primary">{study.timeline}</div>
                        <div className="text-sm text-muted-foreground">Timeline</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="font-semibold text-primary">{study.employees}</div>
                        <div className="text-sm text-muted-foreground">Employees</div>
                      </div>
                    </div>

                    <blockquote className="text-lg italic text-muted-foreground mb-4 border-l-4 border-primary pl-4">
                      "{study.quote}"
                    </blockquote>
                    
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5" />
                      </div>
                      <div>
                        <div className="font-semibold">{study.author}</div>
                        <div className="text-sm text-muted-foreground">{study.role}</div>
                      </div>
                    </div>

                    <Button asChild>
                      <Link href={`/resources/case-studies/${study.id}`}>
                        Read Full Case Study
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>

                  <div className={`rounded-lg border bg-background p-8 ${
                    index % 2 === 1 ? 'lg:col-start-1' : ''
                  }`}>
                    <h4 className="text-xl font-semibold mb-6">Key Results</h4>
                    <ul className="space-y-4">
                      {study.results.map((result, resultIndex) => (
                        <li key={resultIndex} className="flex items-start space-x-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-muted-foreground">{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* All Case Studies */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                More Success Stories
              </h2>
              <p className="text-lg text-muted-foreground">
                Additional case studies across various industries and frameworks
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {caseStudies.filter(study => !study.featured).map((study) => (
                <div key={study.id} className="rounded-lg border bg-background p-8">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                      <Shield className="h-6 w-6 text-primary-foreground" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">{study.company}</h3>
                      <p className="text-muted-foreground">{study.industry}</p>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <span className="inline-block px-3 py-1 text-sm font-medium bg-primary/10 text-primary rounded-full">
                      {study.framework}
                    </span>
                  </div>

                  <p className="text-muted-foreground mb-6">{study.challenge}</p>

                  <div className="space-y-2 mb-6">
                    {study.results.slice(0, 2).map((result, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{result}</span>
                      </div>
                    ))}
                  </div>

                  <Button variant="outline" asChild>
                    <Link href={`/resources/case-studies/${study.id}`}>
                      Read More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 text-primary-foreground">
              Ready to Write Your Success Story?
            </h2>
            <p className="text-lg text-primary-foreground/80 mb-8">
              Join hundreds of organizations that have transformed their compliance processes with Auris GRCOS.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
