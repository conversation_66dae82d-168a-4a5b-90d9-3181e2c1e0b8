import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Users, Target, Award, Globe } from "lucide-react";

export default function CompanyPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Transforming <span className="text-primary">Compliance</span> for Modern Business
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              At Auris Compliance, we&apos;re on a mission to make compliance management simple, efficient, and accessible
              for organizations of all sizes. Our GRCOS platform represents the future of intelligent compliance automation.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/company/mission">
                  Our Mission
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/company/careers">View Careers</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Our Values
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              The principles that guide everything we do at Auris Compliance.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Target className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Excellence</h3>
              <p className="text-muted-foreground">
                We strive for excellence in everything we do, from product development to customer service.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Users className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Collaboration</h3>
              <p className="text-muted-foreground">
                We believe in the power of collaboration, both within our team and with our customers.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Award className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Integrity</h3>
              <p className="text-muted-foreground">
                We operate with the highest standards of integrity and transparency in all our interactions.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Globe className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Innovation</h3>
              <p className="text-muted-foreground">
                We continuously innovate to solve complex compliance challenges with simple, elegant solutions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Company Stats */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-4 text-center">
              <div>
                <div className="text-4xl font-bold text-primary mb-2">500+</div>
                <div className="text-muted-foreground">Companies Trust Us</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">50+</div>
                <div className="text-muted-foreground">Countries Served</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">99.9%</div>
                <div className="text-muted-foreground">Platform Uptime</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">24/7</div>
                <div className="text-muted-foreground">Expert Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Links */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Learn More About Us
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Discover more about our mission, team, and opportunities to join us.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Link href="/company/mission" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Target className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Our Mission
                </h3>
                <p className="text-muted-foreground mb-4">
                  Learn about our mission to transform compliance management for modern businesses.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Read more <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/company/team" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Users className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Our Team
                </h3>
                <p className="text-muted-foreground mb-4">
                  Meet the experts behind Auris Compliance and our innovative GRCOS platform.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Meet the team <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/company/partners" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Globe className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Partners
                </h3>
                <p className="text-muted-foreground mb-4">
                  Discover our technology and business partnerships that enhance our platform.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  View partners <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/company/careers" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Award className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Careers
                </h3>
                <p className="text-muted-foreground mb-4">
                  Join our growing team and help shape the future of compliance management.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  View openings <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/company/auditor-directory" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Users className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Auditor Directory
                </h3>
                <p className="text-muted-foreground mb-4">
                  Access our network of certified auditors and compliance experts.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Browse directory <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/contact" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Globe className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Contact Us
                </h3>
                <p className="text-muted-foreground mb-4">
                  Get in touch with our team for questions, support, or partnership opportunities.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Contact us <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Ready to Join Our Mission?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Whether you&apos;re looking to streamline your compliance or join our team, we&apos;d love to hear from you.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo/request">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/company/careers">View Careers</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
