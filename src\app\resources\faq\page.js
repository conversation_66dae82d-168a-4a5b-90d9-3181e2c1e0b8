"use client";

import <PERSON> from "next/link";
import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { ArrowRight, ChevronDown, ChevronUp, Search, HelpCircle, Shield, Users, Building } from "lucide-react";

export default function FAQPage() {
  const [openItems, setOpenItems] = useState(new Set([0])); // First item open by default
  const [searchTerm, setSearchTerm] = useState("");

  const toggleItem = (index) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  const faqCategories = [
    {
      title: "Platform & Features",
      icon: Building,
      faqs: [
        {
          question: "What is Auris GRCOS and how does it work?",
          answer: "Auris GRCOS (Governance, Risk, and Compliance Operating System) is a unified platform that automates compliance across multiple frameworks. It uses intelligent control mapping to ensure that implementing one control satisfies requirements across multiple compliance standards like ISO 27001, SOC 2, HIPAA, and more."
        },
        {
          question: "Which compliance frameworks does Auris GRCOS support?",
          answer: "We support 50+ compliance frameworks including ISO 27001, SOC 2, HIPAA, PCI DSS, GDPR, NIST CSF, NIST 800-82, FedRAMP, CCPA, ISO 22301, COBIT, and POPIA. Our unified approach means implementing controls once satisfies multiple framework requirements."
        },
        {
          question: "How does the unified control approach work?",
          answer: "Our platform maps controls across frameworks, so when you implement an access control measure, it automatically satisfies ISO 27001 A.9.1.1, SOC 2 CC6.1, PCI DSS 7.1, and NIST CSF PR.AC-1 requirements simultaneously. This eliminates duplicate work and reduces compliance overhead."
        },
        {
          question: "What are the main modules in GRCOS?",
          answer: "GRCOS includes four core modules: LightHouse (data integration), ComplianceCentre (compliance tracking), ActionCentre (task management), and TrustCentre (security transparency). Each module works together to provide comprehensive compliance management."
        }
      ]
    },
    {
      title: "Implementation & Setup",
      icon: Shield,
      faqs: [
        {
          question: "How long does it take to implement Auris GRCOS?",
          answer: "Implementation typically takes 4-8 months depending on your organization size and complexity. Our customers achieve compliance 60-80% faster than traditional approaches. We provide dedicated implementation support and proven methodologies to ensure success."
        },
        {
          question: "Do you provide implementation support?",
          answer: "Yes, we provide comprehensive implementation support including dedicated customer success managers, technical implementation specialists, and access to our certified auditor network. We also offer training programs and ongoing support."
        },
        {
          question: "Can GRCOS integrate with our existing systems?",
          answer: "Absolutely. GRCOS integrates with 200+ popular business applications including cloud platforms, security tools, HR systems, and more. Our LightHouse module handles all data integration and provides APIs for custom integrations."
        },
        {
          question: "What happens to our existing compliance work?",
          answer: "We help migrate your existing compliance documentation and evidence into GRCOS. Our platform can import existing policies, procedures, and audit evidence, then map them to the appropriate framework requirements to avoid starting from scratch."
        }
      ]
    },
    {
      title: "Pricing & Plans",
      icon: Users,
      faqs: [
        {
          question: "How is Auris GRCOS priced?",
          answer: "We offer tiered pricing based on organization size and needs. Startups start at $2,999/month, SMEs at $5,999/month, and Enterprise at $25,000/month. All plans include the core platform, implementation support, and ongoing customer success."
        },
        {
          question: "Is there a free trial available?",
          answer: "We offer personalized demos and proof-of-concept implementations for qualified prospects. Contact our sales team to discuss a trial that fits your specific compliance requirements and timeline."
        },
        {
          question: "What's included in the pricing?",
          answer: "All plans include the full GRCOS platform, unlimited users, implementation support, training, ongoing customer success, and access to our auditor network. Enterprise plans include additional customization and dedicated support."
        },
        {
          question: "Are there any setup or implementation fees?",
          answer: "Implementation support is included in all plans. For complex enterprise deployments requiring extensive customization, additional professional services may be available. We'll discuss all costs upfront during the sales process."
        }
      ]
    }
  ];

  const allFaqs = faqCategories.flatMap((category, categoryIndex) => 
    category.faqs.map((faq, faqIndex) => ({
      ...faq,
      categoryTitle: category.title,
      globalIndex: categoryIndex * 10 + faqIndex
    }))
  );

  const filteredFaqs = searchTerm 
    ? allFaqs.filter(faq => 
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : null;

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Frequently Asked <span className="text-primary">Questions</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Find answers to common questions about Auris GRCOS, implementation, 
              pricing, and compliance best practices.
            </p>
            
            {/* Search Bar */}
            <div className="mt-10 mx-auto max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:border-gray-600 dark:bg-gray-800"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            {searchTerm ? (
              // Search Results
              <div>
                <h2 className="text-2xl font-bold mb-8">
                  Search Results for &quot;{searchTerm}&quot; ({filteredFaqs.length} found)
                </h2>
                {filteredFaqs.length > 0 ? (
                  <div className="space-y-4">
                    {filteredFaqs.map((faq) => (
                      <div key={faq.globalIndex} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                        <button
                          onClick={() => toggleItem(faq.globalIndex)}
                          className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-muted/50 transition-colors"
                        >
                          <div>
                            <h3 className="font-semibold text-lg">{faq.question}</h3>
                            <p className="text-sm text-muted-foreground mt-1">{faq.categoryTitle}</p>
                          </div>
                          {openItems.has(faq.globalIndex) ? (
                            <ChevronUp className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                          )}
                        </button>
                        {openItems.has(faq.globalIndex) && (
                          <div className="px-6 pb-4">
                            <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <HelpCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">No results found</h3>
                    <p className="text-muted-foreground mb-6">
                      Try different keywords or browse categories below.
                    </p>
                    <Button onClick={() => setSearchTerm("")}>
                      Clear Search
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              // Category View
              <div className="space-y-12">
                {faqCategories.map((category, categoryIndex) => {
                  const IconComponent = category.icon;
                  return (
                    <div key={categoryIndex}>
                      <div className="flex items-center space-x-3 mb-8">
                        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                          <IconComponent className="h-6 w-6 text-primary-foreground" />
                        </div>
                        <h2 className="text-2xl font-bold">{category.title}</h2>
                      </div>
                      
                      <div className="space-y-4">
                        {category.faqs.map((faq, faqIndex) => {
                          const globalIndex = categoryIndex * 10 + faqIndex;
                          return (
                            <div key={faqIndex} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                              <button
                                onClick={() => toggleItem(globalIndex)}
                                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-muted/50 transition-colors"
                              >
                                <h3 className="font-semibold text-lg pr-4">{faq.question}</h3>
                                {openItems.has(globalIndex) ? (
                                  <ChevronUp className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                                ) : (
                                  <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                                )}
                              </button>
                              {openItems.has(globalIndex) && (
                                <div className="px-6 pb-4">
                                  <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Still Have Questions?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Can&apos;t find what you&apos;re looking for? Our compliance experts are here to help.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/contact">
                  Contact Support
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">Book Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
