import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Brain,
  Zap,
  Settings,
  Network,
  CheckCircle,
  Bot,
  Target,
  Activity,
  Shield,
  Eye,
  Layers,
  GitBranch,
  Monitor,
  Users,
  Workflow,
  Cpu,
  Database
} from "lucide-react";

export default function MultiAgentOrchestrationPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Multi-Agent <span className="text-primary">Orchestration</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Intelligent automation across all GRC domains
            </p>
            <p className="mt-4 text-base leading-7 text-muted-foreground max-w-3xl mx-auto">
              Deploy intelligent AI agents that work together to automate governance, risk management, 
              and compliance activities across your entire organization, providing coordinated responses 
              and proactive management of complex GRC workflows.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Key Features
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Brain className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Intelligent Coordination</h3>
                <p className="text-muted-foreground">
                  AI agents communicate and coordinate with each other to handle complex 
                  GRC workflows that span multiple domains and require sophisticated decision-making.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Automated Workflows</h3>
                <p className="text-muted-foreground">
                  Streamline complex compliance processes with intelligent automation that 
                  adapts to changing requirements and organizational needs.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Target className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Proactive Risk Management</h3>
                <p className="text-muted-foreground">
                  Agents continuously monitor for risks and automatically initiate 
                  appropriate responses before issues escalate into major problems.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Network className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Cross-Domain Integration</h3>
                <p className="text-muted-foreground">
                  Seamlessly integrate governance, risk, and compliance activities across 
                  different business units, systems, and regulatory frameworks.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Agent Types */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Specialized AI Agents
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Our multi-agent system deploys specialized AI agents, each designed for specific 
              GRC functions while working together as a coordinated team.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Governance Agents */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Settings className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Governance Agents</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Policy management and enforcement</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Workflow orchestration and approval routing</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Stakeholder notification and communication</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Decision support and recommendations</span>
                </li>
              </ul>
            </div>

            {/* Risk Agents */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Shield className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Risk Agents</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Continuous risk assessment and monitoring</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Threat intelligence integration</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Incident response coordination</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Risk mitigation strategy execution</span>
                </li>
              </ul>
            </div>

            {/* Compliance Agents */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <CheckCircle className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Compliance Agents</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Regulatory requirement tracking</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Evidence collection and validation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Audit preparation and support</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Compliance gap identification</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Orchestration Capabilities */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Advanced Orchestration Capabilities
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Our agents work together using advanced coordination mechanisms to deliver 
              sophisticated GRC automation that adapts to your organization's unique needs.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-12">
            {/* Intelligent Coordination */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Brain className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Coordination</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Dynamic task allocation based on agent capabilities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Real-time communication and information sharing</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Conflict resolution and consensus building</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Adaptive workflow optimization</span>
                </li>
              </ul>
            </div>

            {/* Learning and Adaptation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-6">
                <Activity className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Learning and Adaptation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Continuous learning from organizational patterns</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Performance optimization through experience</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Predictive analytics for proactive management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  <span>Self-improving automation capabilities</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Integration Framework */}
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mr-4">
                <Layers className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Enterprise Integration Framework</h3>
                <p className="text-sm text-muted-foreground">Seamless integration with existing systems</p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-start space-x-3">
                <Database className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Data Integration</h4>
                  <p className="text-sm text-muted-foreground">Real-time data synchronization</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Monitor className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">System Monitoring</h4>
                  <p className="text-sm text-muted-foreground">Continuous health checks</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Users className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">Human Collaboration</h4>
                  <p className="text-sm text-muted-foreground">Human-in-the-loop workflows</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <GitBranch className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-sm">API Orchestration</h4>
                  <p className="text-sm text-muted-foreground">Third-party integrations</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Orchestration Benefits
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Operational Efficiency</h3>
                <p className="text-muted-foreground">
                  Dramatically reduce manual effort and human error while accelerating 
                  GRC processes through intelligent automation and coordination.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Eye className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Enhanced Visibility</h3>
                <p className="text-muted-foreground">
                  Gain comprehensive visibility into all GRC activities with real-time 
                  monitoring and intelligent reporting across all domains.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Target className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Proactive Management</h3>
                <p className="text-muted-foreground">
                  Shift from reactive to proactive GRC management with predictive 
                  analytics and automated preventive measures.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Cpu className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Scalable Operations</h3>
                <p className="text-muted-foreground">
                  Scale GRC operations seamlessly as your organization grows without 
                  proportional increases in manual effort or resources.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Ready for Intelligent GRC Automation?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Experience the power of coordinated AI agents working together for your GRC needs.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Schedule Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Join Waitlist</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
